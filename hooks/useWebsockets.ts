'use client';
import { useEffect, useState } from 'react';
import useWebSocket from 'react-use-websocket';
import { useParams, usePathname } from 'next/navigation';
import { WS_BASE_URL } from '@/app/api/constants.ts';
import { INotification } from '@/app/api/server-actions/getNotifications.ts';
import { NOTIFICATIONS_TO_SHOW } from '@/constants.ts';
import { UserType, useUser } from '@/app/wrappers/userProvider.tsx';
import { getCurrencySymbol } from '@/utils/getCurrencySymbol.ts';
import { revalidate } from '@/app/api/server-actions/revalidate.ts';
import { useUserBonuses } from '@/app/wrappers/UserBonusesProvider.tsx';
import { getCurrentUser } from '@/app/api/server-actions/getCurrentUser.ts';
import { getCookie } from '@/app/api/server-actions/getCookies.ts';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation.ts';
import { LanguagesType } from '@/types/global';
import uuid4 from 'uuid4';

const NOTIFICATIONS_BUTTONS = {
  freespins_open: { buttonText: 'play', buttonLink: '' },
  freespins_new: {
    buttonText: 'take',
    buttonLink: '/casino/profile/my-bonuses',
  },
  wager_new: { buttonText: 'take', buttonLink: '/casino/profile/my-bonuses' },
  new_bonus: { buttonText: 'take', buttonLink: '/casino/profile/my-bonuses' },
  confirm_email: {
    buttonText: 'confirm',
    buttonLink: '/casino/profile/personal-data',
  },
  confirm_phone: {
    buttonText: 'confirm',
    buttonLink: '/casino/profile/personal-data',
  },
};

export const addTranslationDataToMessage = (
  notification: INotification,
  user: UserType | null,
  lng: LanguagesType,
) => {
  const userCurrency = getCurrencySymbol(user?.currency || '');
  const message = {
    ...notification,
    translationsData: {},
    ...(NOTIFICATIONS_BUTTONS[
      notification.notification_type as keyof typeof NOTIFICATIONS_BUTTONS
    ] || {}),
  } as INotification;

  if (
    [
      'freespins_open',
      'freespins_new',
      'freespins_cancel',
      'wager_win',
      'wager_open',
      'wager_cancel',
      'wager_lose',
      'wager_new',
      'new_bonus',
    ].includes(notification.notification_type)
  ) {
    message.translationsData.templateName =
      getAvailableTranslation(message.details?.extra?.bonus_title || '', lng) ||
      message.details?.extra?.bonus_name;
  }

  if (
    notification.notification_type === 'freespins_open' ||
    notification.notification_type === 'freespins_new' ||
    notification.notification_type === 'freespins_cancel'
  ) {
    const freespinsData = notification.details?.extra;
    if (notification.notification_type === 'freespins_open') {
      message.buttonLink = `/casino/${freespinsData?.game_provider_slug}/game/${freespinsData?.game_slug}`;
    }
    message.translationsData.spinsAmount = notification.details.spins;
    message.translationsData.count = notification.details.spins;
    message.translationsData.gameName = freespinsData?.game_name;
  } else if (
    notification.notification_type === 'wager_win' &&
    notification.details.cutwin
  ) {
    message.notification_type = 'wager_win_cut';
    message.translationsData.amountInCurrency = `${userCurrency}${notification.details.cutwin}`;
  } else if (notification.notification_type === 'wager_cancel') {
    message.translationsData.amountInCurrency = `${userCurrency}${notification.details.cutwin}`;
  } else if (
    notification.notification_type === 'deposit_completed' &&
    notification.details?.paycode === 4
  ) {
    message.notification_type = 'deposit_completed_tournament';
    message.translationsData.amountInCurrency = `${userCurrency}${notification.details.amount}`;
  } else if (notification.details.amount) {
    message.translationsData.amountInCurrency = `${userCurrency}${notification.details.amount}`;
  } else if (notification.details.level) {
    message.translationsData.level = notification.details.level;
  }

  return message;
};

export const useWebsockets = (
  setNewNotificationsCount: Function,
  setAllNotificationsCount: Function,
) => {
  const [token, setToken] = useState('');
  const { user, setUser } = useUser();
  const { getFilteredBonuses, userBonuses, setInProgressBonuses } =
    useUserBonuses();
  const { lng } = useParams();
  const pathname = usePathname();

  const socketUrl = `${WS_BASE_URL}/ws/notifications?token=${token}`;

  const [newMessage, setNewMessage] = useState<INotification | null>(null);
  const [newBonusesMessages, setNewBonusesMessages] = useState<
    INotification[] | null
  >(null);
  const [newBonusesCount, setNewBonusesCount] = useState(0);

  useEffect(() => {
    if (newBonusesCount === 0 && newBonusesMessages?.length) {
      const commonData = {
        buttonText: 'take',
        buttonLink: '/casino/profile/my-bonuses',
      };

      const notificationsData = {
        id: uuid4(),
        created_at: new Date().toISOString(),
        status: 'new',
        notification_type: 'new_bonuses',
        details: {},
        translationsData: {
          bonusesCount: newBonusesMessages.length,
          count: newBonusesMessages.length,
        },
        ...commonData,
        newBonusesMessages: newBonusesMessages.map((message) => ({
          ...message,
          translationsData: {
            bonusesCount: newBonusesMessages.length,
            count: newBonusesMessages.length,
            templateName:
              getAvailableTranslation(
                message.details?.extra?.bonus_title || '',
                lng,
              ) || message.details?.extra?.bonus_name,
          },
          ...commonData,
        })),
      };
      // console.log('getFilteredBonuses after notification: bonus/bonuses');
      setInProgressBonuses(true);
      getFilteredBonuses();
      setNewMessage(
        newBonusesMessages.length === 1
          ? {
              ...newBonusesMessages[0],
              ...commonData,
              translationsData: {
                templateName:
                  getAvailableTranslation(
                    newBonusesMessages[0].details?.extra?.bonus_title || '',
                    lng,
                  ) || newBonusesMessages[0].details?.extra?.bonus_name,
              },
            }
          : (notificationsData as INotification),
      );
      setNewNotificationsCount(
        (prev: number) => prev + newBonusesMessages.length,
      );
      setAllNotificationsCount(
        (prev: number = 0) => prev + newBonusesMessages.length,
      );
      setNewBonusesMessages(null);
    }
  }, [newBonusesCount, newBonusesMessages]);

  useEffect(() => {
    if (newBonusesMessages?.length === 1) {
      setTimeout(() => {
        setNewBonusesCount(0);
      }, 4000);
    }
  }, [newBonusesMessages]);

  useEffect(() => {
    if (!user?.id) {
      setToken('');
    }
    if (!token && user?.id) {
      setTimeout(() => {
        getCookie('token').then((cookie) => {
          setToken(cookie || '');
        });
      }, 700);
    }
  }, [user, token, pathname]);

  const {
    lastJsonMessage,
    readyState,
  }: { lastJsonMessage: INotification; readyState: -1 | 0 | 1 | 2 | 3 } =
    useWebSocket(
      socketUrl,
      {
        onOpen: () => console.log('opened'),
        //Will attempt to reconnect on all close events, such as server shutting down
        shouldReconnect: () => true,
      },
      !!token,
    );

  useEffect(() => {
    // console.log('NOTIFICATION received', lastJsonMessage);
    if (
      lastJsonMessage &&
      Object.values(NOTIFICATIONS_TO_SHOW)
        .flat()
        .includes((lastJsonMessage as INotification).notification_type)
    ) {
      const message = {
        status: 'new',
        ...addTranslationDataToMessage(lastJsonMessage, user, lng),
      };

      if (message.notification_type === 'new_bonus') {
        setNewBonusesCount((prev: number) => prev + 1);
        setNewBonusesMessages((prev: INotification[] | null) => {
          return [message as INotification, ...(prev || [])];
        });
      }

      if (
        [
          'deposit_completed',
          'deposit_completed_tournament',
          'withdrawal_rollback',
          'withdrawal_rejected',
          'wager_open',
          'wager_cancel',
          'wager_lose',
          'wager_win',
          'wager_win_cut',
          'freespins_open',
          'freespins_new',
          'freespins_cancel',
          'level_up',
        ].includes(message.notification_type)
      ) {
        getCurrentUser().then((user) => {
          setUser(user);
        });
      }

      if (
        userBonuses &&
        [
          'deposit_completed',
          'deposit_completed_tournament',
          'wager_open',
          'wager_cancel',
          'freespins_open',
          'freespins_cancel',
          'wager_new',
          'freespins_new',
          'wager_lose',
          'wager_win',
          // 'new_bonus',
        ].includes(message.notification_type)
      ) {
        // console.log(
        //   'getFilteredBonuses after notification',
        //   message.notification_type,
        // );
        setInProgressBonuses(true);
        getFilteredBonuses();
      }

      if (
        [
          'deposit_completed',
          'deposit_completed_tournament',
          'withdrawal_rejected',
          'withdrawal_completed',
        ].includes(message.notification_type)
      ) {
        revalidate({
          tags: ['pending_withdrawals'],
        });
      }

      if (message.notification_type !== 'new_bonus') {
        setNewMessage(message as INotification);
        setNewNotificationsCount((prev: number) => prev + 1);
        setAllNotificationsCount((prev: number = 0) => prev + 1);
      }
    }
  }, [lastJsonMessage]);

  return {
    newNotification: newMessage,
    setNewNotification: setNewMessage,
    socketReadyState: readyState,
  } as {
    newNotification: INotification;
    setNewNotification: Function;
    socketReadyState: -1 | 0 | 1 | 2 | 3;
  };
};
