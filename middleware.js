import { NextResponse } from 'next/server';
import acceptLanguage from 'accept-language';
import { FALLBACK_LNG, LANGUAGES, LNG_COOKIE_NAME } from './app/i18n/settings';
import { verifyToken } from './app/api/server-actions/verifyToken.ts';
import { ALL_PATHS_WITH_AUTHORIZATION } from './app/config/navMenuEntities.ts';

acceptLanguage.languages(LANGUAGES);

const SKIP_PREFIXES = [
  '/api',
  '/_next/static',
  '/_next/image',
  '/assets',
  '/logos',
  '/favicon.ico',
  '/robots.txt',
  '/manifest.json',
  '/sitemap.xml',
];

const SKIP_EXTENSIONS = /\.(?:jpg|jpeg|png|gif|bmp|svg|webmanifest|webp)$/i;

export async function middleware(req) {
  const { pathname } = req.nextUrl;
  const isRestrictedPath = ALL_PATHS_WITH_AUTHORIZATION.some((path) =>
    pathname.includes(path),
  );

  // 1) Skip all image/static routes
  if (
    SKIP_PREFIXES.some((p) => pathname.startsWith(p)) ||
    SKIP_EXTENSIONS.test(pathname)
  ) {
    return NextResponse.next();
  }
  // console.log(req.nextUrl.href);
  if (
    isRestrictedPath &&
    !req.cookies.get('token')?.value &&
    !req.cookies.get('refresh')?.value
  ) {
    return NextResponse.redirect(new URL('/not-found', req.url));
  }

  const lngInUrl = pathname.split('/')[1];
  const lngInUrlIsSupported = LANGUAGES.some((lang) => lngInUrl === lang);
  const lngFromCookie = req.cookies.get(LNG_COOKIE_NAME)?.value;

  // if language in path is supported, and no cookie is set or lang in cookies and in url differ, set cookie
  if (lngInUrlIsSupported && (!lngFromCookie || lngFromCookie !== lngInUrl)) {
    const response = NextResponse.next();
    response.cookies.set(LNG_COOKIE_NAME, lngInUrl);

    const newAccessToken = await verifyToken(req.cookies, response.cookies);
    if ((!newAccessToken || newAccessToken.error) && isRestrictedPath) {
      return NextResponse.redirect(new URL('/not-found', req.url));
    }

    return response;
  }

  // if no language in url, set cookie to Accept-Language, lng in referer url or fallback
  if (!lngInUrlIsSupported) {
    let lng =
      lngFromCookie || acceptLanguage.get(req.headers.get('Accept-Language'));

    if (!lng && req.headers.has('referer')) {
      const refererUrl = new URL(req.headers.get('referer'));
      const lngInReferer = LANGUAGES.find((l) =>
        refererUrl.pathname.startsWith(`/${l}`),
      );

      lng = lngInReferer;
    }

    if (!lng) {
      lng = FALLBACK_LNG;
    }

    const urlSearchParams = req.nextUrl.searchParams.toString();
    const response = NextResponse.redirect(
      new URL(
        `/${lng}${pathname}${urlSearchParams ? '?' + urlSearchParams : ''}`,
        req.url,
      ),
    );

    if (!lngFromCookie) {
      response.cookies.set(LNG_COOKIE_NAME, lng);
    }

    const newAccessToken = await verifyToken(req.cookies, response.cookies);
    if ((!newAccessToken || newAccessToken.error) && isRestrictedPath) {
      return NextResponse.redirect(new URL('/not-found', req.url));
    }
    // response.headers.set('x-middleware-cache', 'no-cache');
    return response;
  }

  const response = NextResponse.next();
  const newAccessToken = await verifyToken(req.cookies, response.cookies);
  if ((!newAccessToken || newAccessToken.error) && isRestrictedPath) {
    return NextResponse.redirect(new URL('/not-found', req.url));
  }
  // response.headers.set('x-middleware-cache', 'no-cache');
  return response;
}
