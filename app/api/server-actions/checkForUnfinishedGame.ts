'use server';
import { cookies } from 'next/headers';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { fetchData } from '@/app/api/fetchData.ts';

export const checkForUnfinishedGame = async () => {
  let url = `${API_BASE_URL}/platform/user/game_unfinished`;

  const tokenVerified = cookies().get('token')?.value;

  if (!tokenVerified) {
    return null;
  }

  const result = await fetchData({
    url,
    token: tokenVerified,
    type: 'check for unfinished game',
  });

  return result;
};
