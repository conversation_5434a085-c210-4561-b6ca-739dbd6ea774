interface FetchDataProps {
  url: string;
  token?: string;
  method?: 'GET' | 'POST' | 'PATCH';
  withCache?: boolean;
  filters?: { [key: string]: (string | number)[] };
  revalidateTags?: string[];
  type?: string;
  body?: any;
  contentType?: 'application/json' | 'application/x-www-form-urlencoded';
  revalidateTime?: number;
  fetchTimeout?: number;
}

const fetchData = async ({
  url,
  withCache = false,
  revalidateTime,
  token,
  method = 'GET',
  filters,
  revalidateTags,
  type,
  body,
  contentType = 'application/json',
  fetchTimeout = 25000,
}: FetchDataProps) => {
  try {
    if (filters && Object.keys(filters)?.length) {
      let filtersArray: string[] = [];
      Object.keys(filters).forEach((key) => {
        filtersArray.push(`${key}=${filters[key].join('|')}`);
      });
      url = `${url}&filters=${filtersArray.join(',')}`;
    }
    // const isDev = process.env.NODE_ENV === 'development';
    // console.log(isDev);

    const requestOptions: {
      method: 'GET' | 'POST' | 'PATCH';
      cache?: 'no-store' | 'force-cache';
      headers?: { [key: string]: string };
      body?: string;
      next?: { revalidate?: number; tags?: string[] };
      signal?: AbortSignal;
    } = {
      method,
      headers: {
        'Content-Type':
          contentType === 'application/json'
            ? contentType + '; charset=utf-8'
            : contentType,
      },
    };

    // if (fetchTimeout) {
    requestOptions.signal = AbortSignal.timeout(fetchTimeout);
    // }

    if (withCache) {
      requestOptions.next = { revalidate: revalidateTime || 60 };
    } else {
      requestOptions.cache = 'no-store';
    }

    if (revalidateTags?.length) {
      if (!requestOptions.next) {
        requestOptions.next = { tags: revalidateTags };
      } else {
        requestOptions.next.tags = revalidateTags;
      }
    }

    if (token) {
      requestOptions.headers = {
        ...requestOptions.headers,
        Authorization: `Bearer ${token}`,
      };
    }

    if (body) {
      requestOptions.body =
        contentType === 'application/x-www-form-urlencoded'
          ? new URLSearchParams(body).toString()
          : JSON.stringify(body);
    }
    // console.log(url);
    const response = await fetch(url, requestOptions);
    const result = await response.json();

    if (!response.ok) {
      console.error(
        `Error fetching ${type || 'data'}: ${result?.detail?.parameters?.error_text || result?.detail?.message || result?.detail || ''}`,
      );
    }
    return result || {};
  } catch (error) {
    console.log(
      'fetchData error',
      typeof error === 'string'
        ? 'string: ' + error
        : (error as Error)?.name + ': ' + (error as Error)?.message,
    );
    if (
      typeof error === 'string'
        ? error.includes('AbortError') || error.includes('TimeoutError')
        : (error as Error).name === 'AbortError' ||
          (error as Error).name === 'TimeoutError'
    ) {
      console.error('Request timed out, please try again.');
      return {
        error: 'timeoutError',
        detail: {
          message: 'Request timed out, please try again.',
          entity: 'Request',
          parameters: {
            error_text: 'timeout error',
            error_code: 408,
          },
        },
      };
    } else {
      console.error(
        `Fetch error: ${(typeof error === 'string' ? error : (error as Error)?.message) || 'An unknown error occurred.'}`,
      );
      return {
        error:
          (typeof error === 'string' ? error : (error as Error)?.message) ||
          'An unknown error occurred.',
      };
    }
  }
};
export { fetchData };
