'use client';
import { useEffect } from 'react';
import { theme } from '@/theme.ts';
import { UserType } from '@/app/wrappers/userProvider.tsx';
import { usePrevious } from '@/hooks/useReact';
import { LanguagesType } from '@/types/global';
import { useParams } from 'next/navigation';

const appId = process.env.NEXT_PUBLIC_INTERCOM_APP_ID || '';

const IntercomScript = ({
  lng,
  user,
}: {
  lng?: LanguagesType;
  user?: UserType;
}) => {
  const params = useParams();
  const prevUser = usePrevious(user);

  useEffect(() => {
    if (user && prevUser) return;
    const intercomUserData = {} as any;
    if (user) {
      intercomUserData['user_id'] = user.id.toString();
      intercomUserData['level'] = user.level_number;
      intercomUserData['currency'] = user.currency;
    }

    let d = document;
    let i: any = function () {
      i.c(arguments);
    };
    i.q = [];
    i.c = function (args: any) {
      i.q.push(args);
    };
    (window as any).intercomSettings = {
      app_id: appId,
      custom_launcher_selector: '.live_support_button',
      language_override: lng || params?.lng || '',
      background_color: theme.color.primary.darker,
      action_color: theme.color.primary.main,
      // hide_default_launcher: true,
      ...intercomUserData,
    };
    (window as any).Intercom = i;
    let l = function () {
      let _a, _b;
      let s = d.createElement('script');
      s.type = 'text/javascript';
      s.async = true;
      s.src = `https://widget.intercom.io/widget/${(_a = (window as any).intercomSettings) === null || _a === void 0 ? void 0 : _a.app_id}`;
      let x = d.getElementsByTagName('script')[0];
      (_b = x.parentNode) === null || _b === void 0
        ? void 0
        : _b.insertBefore(s, x);
    };
    if (document.readyState === 'complete') {
      l();
    } else if ((window as any).attachEvent) {
      (window as any).attachEvent('onload', l);
    } else {
      window.addEventListener('load', l, false);
    }
  }, [prevUser, user]);

  return null;
};

export default IntercomScript;
