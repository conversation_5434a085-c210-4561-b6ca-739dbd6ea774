import { NextRequest } from 'next/server';

export async function GET(request: NextRequest) {
  const host = request.headers.get('host') || '';
  const directives =
    host.includes('likecasino.com') || host.includes('likecasinomail.com')
      ? [
          'User-agent: *',
          'Allow: /',
          host.includes('likecasino.com')
            ? 'Sitemap: https://likecasino.com/sitemap.xml'
            : 'Sitemap: https://likecasinomail.com/sitemap.xml',
        ]
      : ['User-agent: *', 'Disallow: /'];

  return new Response(directives.join('\n'), {
    status: 200,
    headers: { 'Content-Type': 'text/plain' },
  });
}
