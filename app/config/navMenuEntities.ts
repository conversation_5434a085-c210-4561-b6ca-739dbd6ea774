export const ENTITIES = {
  authorized: [
    {
      key: 'favourites',
      label: 'favourite',
      secondLabel: 'games',
      route: '/favourites',
      iconName: 'heartShape',
      withAuthorization: true,
      iconProps: { stroke: '#CBD5E1', fill: '#CBD5E1' },
    },
    {
      key: 'recent',
      label: 'recent',
      secondLabel: 'games',
      route: '/recent',
      iconName: 'clockIcon',
      withAuthorization: true,
      iconProps: { fill: '#CBD5E1' },
    },
  ],
  all: {
    common: [
      {
        key: 'all',
        label: 'allGames',
        route: '/games/all',
        iconName: 'grid',
        sort: 'popularity=asc', // 1-the highest,best place
        maxLimit: null,
        iconProps: { fill: '#CBD5E1' },
      },
      {
        key: 'top',
        label: 'top',
        secondLabel: 'games',
        route: '/games/top',
        iconName: 'star',
        iconNameOnPage: 'starFlying',
        filter: { top__gte: [0] },
        sort: 'top=asc',
        iconProps: { fill: '#CBD5E1' },
      },
    ],
    casino: [
      {
        key: 'hot',
        label: 'hot',
        secondLabel: 'games',
        route: '/games/hot',
        iconName: 'fire',
        filter: { hot__gte: [0] },
        sort: 'hot=desc',
        maxLimit: null,
        iconNameOnPage: '',
        iconProps: { fill: '#CBD5E1' },
      },
      {
        key: 'liked',
        label: 'liked',
        secondLabel: 'games',
        route: '/games/liked',
        iconName: 'like',
        filter: { manual_rating__gte: [1] },
        sort: 'manual_rating=asc',
        iconProps: { fill: '#CBD5E1' },
      },
      {
        key: 'crash',
        label: 'crash',
        secondLabel: 'games',
        route: '/games/crash',
        iconName: 'rocket',
        filter: { game_type: ['crash'] },
        iconProps: { fill: '#CBD5E1' },
      },
      {
        key: 'new',
        label: 'new',
        secondLabel: 'games',
        route: '/games/new',
        iconName: 'newIcon',
        sort: 'release_date=desc,external_id=desc',
        maxLimit: 100,
        iconProps: { width: 22, height: 22, fill: '#CBD5E1' },
      },
      {
        key: 'rtp',
        label: 'Live RTP',
        route: '/games/rtp',
        iconName: 'graph',
        iconProps: { width: 20, height: 20, fill: '#CBD5E1' },
        filter: { rtp_hot__gte: [0], rtp_cold__gte: [0] },
        sort: '',
      },
      // {
      //   key: 'tournament',
      //   label: 'tournamentss',
      //   secondLabel: 'games',
      //   route: '/games/tournament',
      //   iconName: 'cup',
      //   filter: { categories: ['tournament'] },
      //   iconProps: { fill: '#CBD5E1' },
      // },
      {
        key: 'bonus',
        label: 'bonuss',
        secondLabel: 'games',
        route: '/games/bonus',
        iconName: 'giftBonus',
        filter: { has_bonus: ['true'] },
        iconProps: { fill: '#CBD5E1' },
      },
      {
        key: 'tables',
        label: 'tables',
        secondLabel: 'games',
        route: '/games/tables',
        iconName: 'dices',
        iconProps: { width: 22, height: 22, fill: '#CBD5E1' },

        filter: { game_type: ['roulette|blackjack|baccarat'] },
      },
    ],
    'live-casino': [
      {
        key: 'roulette',
        label: 'roulette',
        route: '/games/roulette',
        iconName: 'roulette',
        filter: { game_type: ['roulette'] },
        maxLimit: null,
        sort: '',
        iconNameOnPage: '',
        iconProps: { fill: '#CBD5E1' },
      },
      {
        key: 'blackjack',
        label: 'blackjack',
        route: '/games/blackjack',
        iconName: 'cards',
        filter: { game_type: ['blackjack'] },
        sort: '',
        iconProps: { fill: '#CBD5E1' },
      },
      {
        key: 'baccarat',
        label: 'baccarat',
        route: '/games/baccarat',
        iconName: 'man',
        filter: { game_type: ['baccarat'] },
        iconProps: { fill: '#CBD5E1' },
      },
      {
        key: 'videoPoker',
        label: 'videoPoker',
        route: '/games/video-poker',
        iconName: 'chipPlay',
        filter: { game_type: ['video_poker'] },
        iconProps: { fill: '#CBD5E1' },
      },
      {
        key: 'showsGames',
        label: 'showsGames',
        route: '/games/shows',
        iconName: 'videoPlay',
        filter: { game_type: ['tv_shows'] },
        iconProps: { fill: '#CBD5E1' },
      },
      {
        key: 'other',
        label: 'other',
        route: '/games/other',
        iconName: 'other',
        filter: { game_type: ['other'] },
        iconProps: { fill: '#CBD5E1' },
      },
    ],
  },
  general: [
    {
      key: 'helpCenter',
      label: 'helpCenter',
      route: '/help-center',
      iconName: 'infoIcon',
      className: 'helpCenter',
      iconProps: { width: 20, height: 20, fill: '#CBD5E1' },
    },
  ],
  providers: [
    {
      key: 'providers',
      label: 'providers',
      route: '/providers',
      iconName: 'cyberNetwork',
      iconProps: { width: 20, height: 20, fill: '#CBD5E1' },
    },
  ],
};

export const ALL_PATHS_WITH_AUTHORIZATION = ENTITIES.authorized
  .map((entity) => entity.route)
  .concat(['profile'])
  .concat(['cashier'])
  .concat(['deposit']);
