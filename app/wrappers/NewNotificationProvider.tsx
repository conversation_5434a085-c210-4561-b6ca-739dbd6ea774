'use client';
import {
  useContext,
  createContext,
  ReactNode,
  useState,
  useEffect,
} from 'react';
import {
  addTranslationDataToMessage,
  useWebsockets,
} from '@/hooks/useWebsockets.ts';
import {
  getNotifications,
  INotification,
} from '@/app/api/server-actions/getNotifications.ts';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { useParams } from 'next/navigation';

export const useNewNotification = () => useContext(NewNotificationContext);

export const NewNotificationProvider = ({
  children,
}: {
  children: ReactNode;
}) => {
  const newNotification = useNewNotificationContext();

  return (
    <NewNotificationContext.Provider value={newNotification}>
      {children}
    </NewNotificationContext.Provider>
  );
};

const useNewNotificationContext = () => {
  const { user } = useUser();
  const { lng } = useParams();

  const [allNotifications, setAllNotifications] = useState<
    INotification[] | null
  >(null);
  const [newNotificationsCount, setNewNotificationsCount] = useState(0);
  const [allNotificationsCount, setAllNotificationsCount] = useState<
    number | null
  >(null);
  const [inProgress, setInProgress] = useState(true);

  useEffect(() => {
    if (!user?.id) {
      setAllNotifications(null);
      setNewNotificationsCount(0);
      setAllNotificationsCount(null);
      setInProgress(true);
    }
  }, [user?.id]);

  const getAllNotifications = () => {
    getNotifications()
      .then((res) => {
        // console.log('notifications', res);
        if (!res?.items) {
          setAllNotificationsCount(0);
          setInProgress(false);
          return;
        }
        const itemsWithData = res?.items?.map((item) =>
          addTranslationDataToMessage(item, user, lng),
        );

        setAllNotifications(itemsWithData || []);

        setNewNotificationsCount(
          res.items?.filter((item) => item.status !== 'read').length,
        );
        setAllNotificationsCount(res.total || 0);
      })
      .catch((error) => {
        console.error('notifications error:', error);
        setAllNotificationsCount(0);
        setInProgress(false);
      })
      .finally(() => {
        setInProgress(false);
      });
  };

  const { newNotification, setNewNotification, socketReadyState } =
    useWebsockets(setNewNotificationsCount, setAllNotificationsCount);

  return {
    newNotification,
    newNotificationsCount,
    setNewNotificationsCount,
    setAllNotificationsCount,
    allNotificationsCount,
    allNotifications,
    setNewNotification,
    inProgress,
    setInProgress,
    getAllNotifications,
    setAllNotifications,
    socketReadyState,
  };
};

const NewNotificationContext = createContext<
  ReturnType<typeof useNewNotificationContext>
>({
  newNotification: {
    notification_type: '',
    id: 0,
    details: {},
    created_at: '',
    translationsData: {},
  },
  newNotificationsCount: 0,
  setNewNotificationsCount: () => 0,
  setAllNotificationsCount: () => 0,
  allNotificationsCount: null,
  allNotifications: [],
  getAllNotifications: () => {},
  setNewNotification: () => ({}),
  inProgress: false,
  setInProgress: () => false,
  setAllNotifications: () => null,
  socketReadyState: -1,
});
