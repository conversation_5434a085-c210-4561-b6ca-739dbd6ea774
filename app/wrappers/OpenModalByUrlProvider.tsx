'use client';
import { createContext, ReactNode, useContext, useState } from 'react';
import { useOpenModalByUrl } from '@/hooks/useOpenModalByUrl.ts';

const OpenModalByUrlContext = createContext<Function>(() => '');

export const useRegisterModalToOpenByUrl = () =>
  useContext(OpenModalByUrlContext);

export function OpenModalByUrlProvider({ children }: { children: ReactNode }) {
  const [mods, setMods] = useState<Array<string>>([]);

  useOpenModalByUrl(mods);

  const register = (id: string) => {
    if (mods.includes(id)) return;
    setMods((prev) => [...prev, id]);
  };

  return (
    <OpenModalByUrlContext.Provider value={register}>
      {children}
    </OpenModalByUrlContext.Provider>
  );
}

export default OpenModalByUrlProvider;
