'use client';
import {
  useContext,
  createContext,
  ReactNode,
  useState,
  useEffect,
} from 'react';
import {
  getUserBonuses,
  IBonus,
  IBonusGlobalType,
} from '@/app/api/server-actions/bonuses.ts';
import { ActiveWagerType, useUser } from '@/app/wrappers/userProvider.tsx';
import { useInterval } from '@/hooks/useInterval.ts';

export const filterBonuses = (
  bonuses: IBonus[],
  activeBonusTemplateId?: number,
  activeBonusType?: IBonusGlobalType,
  activeWagerFromUserData?: ActiveWagerType | null,
) => {
  return bonuses.filter((bonus: IBonus) => {
    if (
      bonus.external_id === activeBonusTemplateId &&
      bonus.bonus_category === 'cron' &&
      bonus.bonus_global_type === 'template'
    ) {
      return true;
    }

    return (
      !(
        (bonus.bonus_global_type === activeBonusType ||
          bonus.bonus_global_type === 'template') &&
        bonus.external_id === activeBonusTemplateId
      ) &&
      !(
        (
          activeBonusTemplateId === 0 &&
          bonus.bonus_global_type === 'wager_no_deposit' &&
          (activeBonusType === 'wager' ||
            activeBonusType === 'wager_no_deposit') &&
          activeWagerFromUserData?.wager_amount === bonus.wager_amount &&
          activeWagerFromUserData?.multiplier === bonus.multiplier &&
          activeWagerFromUserData?.max_win_sum === bonus.max_win_sum
        ) // bonus without template
      ) &&
      !(
        bonus.bonus_category?.startsWith('deposit_') &&
        bonus.errors?.includes(61) &&
        !bonus.errors?.includes(56)
      )
    ); // deposit_n and not available (nth bonus already taken) and is not the next one which we show
  });
};

export const useUserBonuses = () => useContext(UserBonusesContext);

export const UserBonusesProvider = ({ children }: { children: ReactNode }) => {
  const userBonuses = useUserBonusesContext();

  return (
    <UserBonusesContext.Provider value={userBonuses}>
      {children}
    </UserBonusesContext.Provider>
  );
};

const useUserBonusesContext = () => {
  const { user } = useUser();

  const [userBonuses, setUserBonuses] = useState<IBonus[] | null>(null);
  const [inProgress, setInProgress] = useState(false);

  const [timerKey, setTimerKey] = useState(0);

  const getFilteredBonuses = (
    bonusTemplateId?: number,
    activeBonusType?: IBonusGlobalType,
  ) => {
    if (!user?.id || inProgress) {
      return;
    }
    // console.log('getUserBonuses');
    setInProgress(true);
    getUserBonuses()
      .then((bonuses) => {
        // console.log('user bonuses', bonuses);
        if (!bonuses) {
          setUserBonuses([]);
          return;
        }
        const { active_wager, active_freespins } = user;
        const activeBonusTemplateId =
          active_wager || active_freespins
            ? active_wager?.template_id || active_freespins?.template_id || 0
            : undefined;
        const bonusesFiltered = filterBonuses(
          bonuses,
          bonusTemplateId === undefined
            ? activeBonusTemplateId
            : bonusTemplateId,
          activeBonusType === undefined
            ? user.active_bonus_type
            : activeBonusType,
          user.active_wager,
        );

        setUserBonuses(bonusesFiltered || []);
        setTimerKey((prev) => prev + 1);
      })
      .catch((error) => {
        console.error('getUserBonuses error:', error);
        setUserBonuses([]);
      })
      .finally(() => {
        setInProgress(false);
      });
  };

  const timeoutId = useInterval(
    user?.id
      ? () => {
          // console.log('update bonuses via interval');
          getFilteredBonuses();
        }
      : null,
    user?.id ? 1800 * 1000 : null,
    [timerKey],
  );

  useEffect(() => {
    if (!user?.id) {
      setUserBonuses(null);
      clearInterval(timeoutId);
      return;
    }
  }, [user?.id]);

  return {
    userBonuses,
    setUserBonuses,
    getFilteredBonuses,
    inProgress,
    setInProgressBonuses: setInProgress,
  };
};

const UserBonusesContext = createContext<
  ReturnType<typeof useUserBonusesContext>
>({
  userBonuses: null,
  setUserBonuses: () => null,
  getFilteredBonuses: () => null,
  setInProgressBonuses: () => null,
  inProgress: false,
});
