'use client';
import {
  useContext,
  createContext,
  ReactNode,
  useEffect,
  useState,
} from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import { usePrevious } from '@/hooks/useReact';
import { closeAllOpenedModals } from '@/utils/closeAllOpenedModals.ts';

export const useNavigation = () => useContext(NavigationContext);

export const NavigationProvider = ({ children }: { children: ReactNode }) => {
  const navigation = useNavigationContext();

  return (
    <NavigationContext.Provider value={navigation}>
      {children}
    </NavigationContext.Provider>
  );
};

const useNavigationContext = () => {
  const [activeAuthTab, setActiveAuthTab] = useState<'login' | 'signUp'>(
    'login',
  );
  const [activeModalData, setActiveModalData] = useState<any>(null);

  // const [pendingTransitionPage, setPendingTransitionPage] = useState('');

  const pathname = usePathname();
  const searchParams = useSearchParams();
  const searchString = searchParams.toString();
  const previousRoute =
    usePrevious(pathname + (searchString ? '?' : '') + searchString) || '';

  useEffect(() => {
    window.scroll(0, 0);

    closeAllOpenedModals();
  }, [pathname]);

  return {
    previousRoute,
    pathname,
    activeAuthTab,
    setActiveAuthTab,
    activeModalData,
    setActiveModalData,
    searchString,
    // pendingTransitionPage,
    // setPendingTransitionPage,
  };
};

const NavigationContext = createContext<
  ReturnType<typeof useNavigationContext>
>({
  previousRoute: '',
  pathname: '',
  activeAuthTab: 'login',
  setActiveAuthTab: () => {},
  activeModalData: null,
  setActiveModalData: () => null,
  searchString: '',
  // pendingTransitionPage: '',
  // setPendingTransitionPage: () => {},
});
