'use client';

import { createContext, useContext, useEffect, ReactNode } from 'react';
import Script from 'next/script';
import { verifyRecaptcha } from '@/app/api/verifyRecaptcha';
import { RECAPTCHA_V3_THRESHOLD } from '@/constants';
import { getIp } from '@/app/api/getIP';

declare global {
  // eslint-disable-next-line no-unused-vars
  interface Window {
    recaptchaOptions?: {
      useRecaptchaNet: boolean;
    };
    grecaptcha: {
      // eslint-disable-next-line no-unused-vars
      ready: (callback: () => void) => void;
      execute: (
        // eslint-disable-next-line no-unused-vars
        siteKey: string,
        // eslint-disable-next-line no-unused-vars
        options: { action: string },
      ) => Promise<string>;
    };
  }
}

interface ReCaptchaContextType {
  // eslint-disable-next-line no-unused-vars
  checkRecaptchaV3: (action: string) => Promise<boolean>;
  // eslint-disable-next-line no-unused-vars
  checkRecaptchaV2: (token: string) => Promise<boolean>;
  // eslint-disable-next-line no-unused-vars
  // setRecaptchaToken: (token: string | null) => void;
  // recaptchaToken: string | null;
}

const ReCaptchaContext = createContext<ReCaptchaContextType | undefined>(
  undefined,
);

export const useReCaptcha = (): ReCaptchaContextType => {
  const context = useContext(ReCaptchaContext);
  if (!context) {
    throw new Error('useReCaptcha must be used within a ReCaptchaProvider');
  }
  return context;
};

export const ReCaptchaProvider = ({ children }: { children: ReactNode }) => {
  // const [recaptchaToken, setRecaptchaToken] = useState<string | null>(null);

  // if google.com is blocked/unavailable, use recaptcha.net
  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.recaptchaOptions = {
        useRecaptchaNet: true,
      };
    }
  }, []);

  const checkRecaptchaV3 = async (action: string): Promise<boolean> => {
    // bypass recaptcha for specific ips
    if (process.env.NEXT_PUBLIC_RECAPTCHA_BYPASS_IPS) {
      const bypassIps = process.env.NEXT_PUBLIC_RECAPTCHA_BYPASS_IPS.split(
        ',',
      ).map((ip) => ip.trim());
      const ip = await getIp();
      if (bypassIps.includes(ip)) {
        return true;
      }
    }

    for (let attempt = 0; attempt < 3; attempt++) {
      if (typeof window.grecaptcha !== 'undefined') {
        const token = await window.grecaptcha.execute(
          process.env.NEXT_PUBLIC_RECAPTCHA_V3_PUBLIC_KEY || '',
          { action },
        );
        const response = await verifyRecaptcha(
          'v3',
          token,
          window.location.origin,
        );
        console.log(response, 'v3');
        return response?.score && response?.score >= RECAPTCHA_V3_THRESHOLD
          ? true
          : false;
      }
      // wait before retrying if recaptcha Script is not yet loaded
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }
    return false;
  };

  const checkRecaptchaV2 = async (token: string): Promise<boolean> => {
    const response = await verifyRecaptcha('v2', token, window.location.origin);
    console.log(response, 'v2');
    return response?.success ? true : false;
  };

  return (
    <>
      <Script
        src={`https://www.google.com/recaptcha/api.js?render=${process.env.NEXT_PUBLIC_RECAPTCHA_V3_PUBLIC_KEY}`}
        strategy='afterInteractive'
      />
      <ReCaptchaContext.Provider
        value={{
          checkRecaptchaV3,
          checkRecaptchaV2,
          // recaptchaToken,
          // setRecaptchaToken,
        }}
      >
        {children}
      </ReCaptchaContext.Provider>
    </>
  );
};

export default ReCaptchaProvider;
