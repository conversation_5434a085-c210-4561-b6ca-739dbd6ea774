import { MetadataRoute } from 'next';
import { LANGUAGES } from '@/app/i18n/settings';
import { getGames } from '@/app/api/getGames';
import { getProviders } from '@/app/api/getProviders';
import { getPosts } from '@/app/api/getPosts';
import { getPromotions } from '@/app/api/getPromotions';
import { getTournaments } from '@/app/api/getTournaments';

const BASE_URL = 'https://likecasino.com';

// Game page types
const GAME_PAGE_TYPES = ['casino', 'live-casino'];

// Static game categories from navMenuEntities
const GAME_CATEGORIES = [
  'all',
  'top',
  'new',
  'rtp',
  'tournament',
  'bonus',
  'slots',
  'jackpot',
  'megaways',
  'buy-bonus',
  'table',
  'roulette',
  'blackjack',
  'baccarat',
  'poker',
  'shows',
  'other',
];

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const sitemap: MetadataRoute.Sitemap = [];

  // Add homepage for each language
  LANGUAGES.forEach((lang) => {
    sitemap.push({
      url: `${BASE_URL}/${lang}`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 1.0,
    });
  });

  // Add game page types (casino, live-casino) for each language
  LANGUAGES.forEach((lang) => {
    GAME_PAGE_TYPES.forEach((gamePageType) => {
      sitemap.push({
        url: `${BASE_URL}/${lang}/${gamePageType}`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.9,
      });
    });
  });

  // Add game categories for each language and game page type
  LANGUAGES.forEach((lang) => {
    GAME_PAGE_TYPES.forEach((gamePageType) => {
      GAME_CATEGORIES.forEach((category) => {
        sitemap.push({
          url: `${BASE_URL}/${lang}/${gamePageType}/games/${category}`,
          lastModified: new Date(),
          changeFrequency: 'daily',
          priority: 0.8,
        });
      });
    });
  });

  // Add static pages for each language and game page type
  const staticPages = [
    'help-center',
    'levels',
    'promotions',
    'providers',
    'tournaments',
    'top-wins',
  ];

  LANGUAGES.forEach((lang) => {
    GAME_PAGE_TYPES.forEach((gamePageType) => {
      staticPages.forEach((page) => {
        sitemap.push({
          url: `${BASE_URL}/${lang}/${gamePageType}/${page}`,
          lastModified: new Date(),
          changeFrequency: 'weekly',
          priority: 0.7,
        });
      });
    });
  });

  // Add info pages (if they exist)
  LANGUAGES.forEach((lang) => {
    GAME_PAGE_TYPES.forEach((gamePageType) => {
      const infoPages = ['terms-conditions', 'privacy-policy', 'responsible-gaming', 'loyalty-program'];
      infoPages.forEach((page) => {
        sitemap.push({
          url: `${BASE_URL}/${lang}/${gamePageType}/info/${page}`,
          lastModified: new Date(),
          changeFrequency: 'monthly',
          priority: 0.4,
        });
      });
    });
  });

  // Sort sitemap by priority (highest first) and then by URL
  sitemap.sort((a, b) => {
    if (a.priority !== b.priority) {
      return (b.priority || 0) - (a.priority || 0);
    }
    return a.url.localeCompare(b.url);
  });

  return sitemap;
}
