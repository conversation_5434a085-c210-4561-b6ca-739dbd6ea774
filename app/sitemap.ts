import { MetadataRoute } from 'next';
import { LANGUAGES } from '@/app/i18n/settings';

// const BASE_URL = 'https://likecasino.com';
const ANOTHER_BASE_URL = 'https://likecasinomail.com';

// Game page types
// const GAME_PAGE_TYPES = ['casino', 'live-casino'];

// Static game categories from navMenuEntities
// const GAME_CATEGORIES = [
//   'all',
//   'top',
//   'hot',
//   'liked',
//   'crash',
//   'new',
//   'rtp',
//   'tournament',
//   'bonus',
//   'tables',
//   'roulette',
//   'blackjack',
//   'baccarat',
//   'video-poker',
//   'shows',
//   'other',
// ];

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const sitemap: MetadataRoute.Sitemap = [];

  // Add homepage for each language
  // LANGUAGES.forEach((lang) => {
  //   sitemap.push({
  //     url: `${BASE_URL}/${lang}`,
  //     lastModified: new Date(),
  //     changeFrequency: 'daily',
  //     priority: 1.0,
  //   });
  // });

  // Add game page types (casino, live-casino) for each language
  // LANGUAGES.forEach((lang) => {
  //   GAME_PAGE_TYPES.forEach((gamePageType) => {
  //     sitemap.push({
  //       url: `${BASE_URL}/${lang}/${gamePageType}`,
  //       lastModified: new Date(),
  //       changeFrequency: 'monthly',
  //       priority: 0.6,
  //     });
  //   });
  // });

  // Add game categories for each language and game page type
  // LANGUAGES.forEach((lang) => {
  //   GAME_PAGE_TYPES.forEach((gamePageType) => {
  //     GAME_CATEGORIES.forEach((category) => {
  //       sitemap.push({
  //         url: `${BASE_URL}/${lang}/${gamePageType}/games/${category}`,
  //         lastModified: new Date(),
  //         changeFrequency: 'daily',
  //         priority: 0.9,
  //       });
  //     });
  //   });
  // });

  // Add provider-filtered game category links
  const providerFilteredRoutes = [
    // Casino routes with provider_id=35
    { gamePageType: 'casino', category: 'crash', providerId: 35 },
    { gamePageType: 'casino', category: 'new', providerId: 35 },
    { gamePageType: 'casino', category: 'tables', providerId: 35 },
    // Live casino routes with provider_id=39
    { gamePageType: 'live-casino', category: 'blackjack', providerId: 39 },
  ];

  LANGUAGES.forEach((lang) => {
    providerFilteredRoutes.forEach(({ gamePageType, category, providerId }) => {
      sitemap.push({
        url: `${ANOTHER_BASE_URL}/${lang}/${gamePageType}/games/${category}?filters=provider_id=${providerId}`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.8,
      });
    });
  });

  // Add static pages for each language and game page type
  // const staticPages = [
  //   'help-center',
  //   'levels',
  //   'promotions',
  //   'providers',
  //   'tournaments',
  //   'top-wins',
  // ];
  //
  // LANGUAGES.forEach((lang) => {
  //   GAME_PAGE_TYPES.forEach((gamePageType) => {
  //     staticPages.forEach((page) => {
  //       sitemap.push({
  //         url: `${BASE_URL}/${lang}/${gamePageType}/${page}`,
  //         lastModified: new Date(),
  //         changeFrequency: 'weekly',
  //         priority: 0.7,
  //       });
  //     });
  //   });
  // });

  // Add info pages (if they exist)
  // LANGUAGES.forEach((lang) => {
  //   GAME_PAGE_TYPES.forEach((gamePageType) => {
  //     const infoPages = [
  //       'terms-conditions',
  //       'privacy-policy',
  //       'responsible-gaming',
  //       'loyalty-program',
  //       'bonus-terms',
  //       'payment-methods',
  //       'kyc-policy',
  //     ];
  //     infoPages.forEach((page) => {
  //       sitemap.push({
  //         url: `${BASE_URL}/${lang}/${gamePageType}/info/${page}`,
  //         lastModified: new Date(),
  //         changeFrequency: 'monthly',
  //         priority: 0.4,
  //       });
  //     });
  //   });
  // });

  // Sort sitemap by priority (highest first) and then by URL
  // sitemap.sort((a, b) => {
  //   if (a.priority !== b.priority) {
  //     return (b.priority || 0) - (a.priority || 0);
  //   }
  //   return a.url.localeCompare(b.url);
  // });

  return sitemap;
}
