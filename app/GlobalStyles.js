'use client';
import { NAV_MENU_WIDTH } from '@/constants.ts';
import { createGlobalStyle } from 'styled-components';
import getTokens from '@/utils/getTokens.js';

const GlobalStyles = createGlobalStyle`
  //scrollbar styling

  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  ::-webkit-scrollbar-track {
    background-color: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: ${({ theme }) => theme.color?.general.dark};
    border-radius: 10px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #667085;
  }

  ::-webkit-scrollbar:horizontal {
    width: 0;
    height: 0;
  }

  *:hover::-webkit-scrollbar-thumb {
    background-color: #667085;
  }

  main {
    flex: 1; /* this made to stick footer at the bottom in case content does not take full page*/
    background-color: ${({ theme }) => theme.color?.general.darkest};
    overflow-y: auto;
    overflow-x: hidden;
    width: calc(100vw - ${NAV_MENU_WIDTH});

    .content-wrapper {
      min-height: ${({ theme }) => (theme.isTouchDevice ? 'calc(100vh - 270px)' : 'calc(100dvh - 270px)')};
      @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
        padding-left: ${({ theme }) => (theme.isTouchDevice ? '24px' : 0)};
      }

    }

    @media only screen and (max-width: ${({ theme }) => theme.breakpoints?.md}px) {
      width: 100vw;
    }
  }

  :root,
  html {
    overflow-x: hidden;
    background-color: ${({ theme }) => theme.color?.general.darkest};
    font-family: ${({ theme: { font } }) => {
      return `${font.family.primary.style.fontFamily}, ${font.family.customUsz.style.fontFamily}`;
    }};

    &.blocked {
      overflow: hidden;
      touch-action: none;
    }
  }

  body {
    //display: flex; /* this made to stick footer at the bottom in case content does not take full page*/
    //flex-direction: column; /* this made to stick footer at the bottom in case content does not take full page*/
    //min-height: 100dvh;
    //overscroll-behavior: none;
    //touch-action: pan-y;
    //position: relative;
    background-color: ${({ theme }) => theme.color?.general.darkest};

    &.blocked {
      overflow: clip;
      touch-action: none;
    }

    &:has(.drawer.opened) {
      overflow: clip;
    }
  }

  #__next {
    display: flex;
    flex-direction: column;
    height: 100dvh;
  }

  // safe area insets for iOS/Safari
  //     @supports (padding: env(safe-area-inset-left)) and (max-width: ${({ theme }) => theme.breakpoints?.md}px) {
  //         &.globalNotification {
  //             padding-left: calc(env(safe-area-inset-left) + 0.5rem) !important;
  //             padding-right: calc(env(safe-area-inset-right) + 2.5rem) !important;
  //             .globalNotificationCloseButton {
  //                 right: calc(env(safe-area-inset-right) + 0.5rem) !important;
  //             }
  //         }
  //         &header,
  //         &.pageMenuFixed,
  //         &main {
  //             padding-left: max(env(safe-area-inset-left), 8px) !important;
  //             padding-right: max(env(safe-area-inset-right), 8px) !important;
  //         }
  //         &.modalDialog {
  //             padding-left: max(env(safe-area-inset-left), 1.5rem) !important;
  //             padding-right: max(env(safe-area-inset-right), 1.5rem) !important;
  //             .closeButtonModal {
  //                 right: calc(env(safe-area-inset-right) + 0.5rem) !important;
  //             }
  //         }
  //         &#gameSearchContainer,
  //         &#cashierModal,
  //         &#signOutModal {
  //             padding-left: env(safe-area-inset-left) !important;
  //             padding-right: env(safe-area-inset-right) !important;
  //             .cashierHeader {
  //                 position: absolute;
  //                 left: env(safe-area-inset-left) !important;
  //                 right: env(safe-area-inset-right) !important;
  //             }
  //         }
  //         &#signOutModal {
  //             .closeButtonModal{
  //                 right: calc(env(safe-area-inset-right) + 1rem) !important;
  //             }
  //         }
  //     }

  .displayNone {
    display: none;
  }

  .loadingScreen {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    flex: 1;
    height: 100dvh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;

    img {
      max-width: 20vh;
      margin-top: -70px;
    }
  }

  .scrollBarHidden::-webkit-scrollbar {
    display: none;
  }

  *:focus-visible {
    outline: none;
  }

  .column {
    flex-direction: column;
  }

  .scrolledTop {
    border-top: 1px solid ${({ theme }) => theme.color?.general.dark};
  }

  .pageMenuFixed {
    position: fixed;
    top: 55px;
    left: 0;
    z-index: 50;
    border-bottom: 1px solid ${({ theme }) => theme.color?.general.dark};

    & ~ .winningsCarousel {
      margin-top: 43px;
    }
  }

  .gamePage {
    .iframeZone {
      @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
        padding-left: ${({ theme }) => (theme.isTouchDevice ? '0' : '24px')};
      }
    }
  }

  .gamesPage,
  .providersPage,
  .faqPage,
  .helpCenterPage,
  .postPage,
  .landingWrapper,
  .historyPage,
  .promotionsPage,
  .levelsPage,
  .topWinsPage,
  .tournamentsPage {
    padding: 0 8px 8px 8px;

    .navigation-wrapper.banners {
      height: 260px;
      margin-bottom: 8px;
    }

    .fader {
      border-radius: 10px;
      height: 100%;
    }

    @media only screen and (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
      padding: 8px 24px 8px 0;
      .navigation-wrapper.banners {
        height: 320px;
        margin-bottom: 30px;
      }
    }
  }

  .landingWrapper {
    background-color: ${({ theme }) => theme.color?.general.darkest};

    .winningsCarousel,
    .promotionsCarousel,
    .gameCarousel,
    .tournamentsCarousel {
      margin-bottom: 18px;
      @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
        margin-bottom: 24px;
      }
    }

    .keen-slider {
      touch-action: pan-y;

      .keen-slider__slide {
        border-radius: 10px;
      }
    }

    //.promotionsCarousel {
    //  & .keen-slider:not([data-keen-slider-disabled]) {
    //    overflow-x: auto !important;
    //    touch-action: initial !important;
    //  }
    //}
  }


  .levelsPage {
    .navigation-wrapper {
      .dots {
        position: relative;
        padding: 8px 0;
        @media (min-width: 1200px) {
          display: none;
        }

        .dot {
          width: 4px !important;
          height: 4px;
          margin: 0 8px;

        }
      }
    }
  }

  .postPage, .faqPage, #promotionModal, .tournamentDescription {
    h1:not(.typography) {
      ${({ theme }) => getTokens(`typography-h1-black-small`, theme)};
      @media only screen and (min-width: ${({ theme }) => theme.breakpoints?.sm}px) {
        ${({ theme }) => getTokens(`typography-h1-black-medium`, theme)};
      }
      @media only screen and (min-width: ${({ theme }) => theme.breakpoints?.lg}px) {
        ${({ theme }) => getTokens(`typography-h1-black-large`, theme)};
      }
    }

    h2:not(.typography) {
      ${({ theme }) => getTokens(`typography-h2-black-small`, theme)};
      @media only screen and (min-width: ${({ theme }) => theme.breakpoints?.sm}px) {
        ${({ theme }) => getTokens(`typography-h2-black-medium`, theme)};
      }
      @media only screen and (min-width: ${({ theme }) => theme.breakpoints?.lg}px) {
        ${({ theme }) => getTokens(`typography-h2-black-large`, theme)};
      }
    }

    h3:not(.typography) {
      ${({ theme }) => getTokens(`typography-h3-black-small`, theme)};
      @media only screen and (min-width: ${({ theme }) => theme.breakpoints?.sm}px) {
        ${({ theme }) => getTokens(`typography-h3-black-medium`, theme)};
      }
      @media only screen and (min-width: ${({ theme }) => theme.breakpoints?.lg}px) {
        ${({ theme }) => getTokens(`typography-h3-black-large`, theme)};
      }
    }

    h4:not(.typography) {
      ${({ theme }) => getTokens(`typography-h3-black-small`, theme)};
      @media only screen and (min-width: ${({ theme }) => theme.breakpoints?.sm}px) {
        ${({ theme }) => getTokens(`typography-h3-black-medium`, theme)};
      }
      @media only screen and (min-width: ${({ theme }) => theme.breakpoints?.lg}px) {
        ${({ theme }) => getTokens(`typography-h3-black-large`, theme)};
      }
    }


    h1, h2, h3, h4 {
      strong {
        font-weight: inherit;
      }
    }

    li {
      > div {
        display: inline;
      }
    }

    ol {
      list-style: auto;
      padding: 0 28px;
    }

    ul {
      list-style: disc;
      padding: 0 25px;
    }
  }


  .postPage a,
  .faqPage a,
  #promotionModal a,
  .tournamentDescription a {
    color: #0083ff;
    letter-spacing: 1px;
    cursor: pointer;
    text-decoration: underline;
  }

  .shadowScreen {
    display: none;
    background-color: ${({ theme }) => `${theme.color?.general.darkest}E5`};
    width: 100vw;
    height: 100vh;
    position: fixed;
    z-index: 110;
    top: 0;

    &.blured {
      background-color: rgba(255, 255, 255, 0.01);
      backdrop-filter: blur(2.5px);
      z-index: 90;
    }

    &.onTop {
      z-index: 210;

      &.zIndexIncreased {
        z-index: 212;
      }
    }
  }

  .shadowScreen.shown {
    display: block;
  }

  .flex.flex-col.overflow-y-scroll::-webkit-scrollbar {
    width: 0;
  }

  .toTopButton {
    position: fixed;
    bottom: 64px;
    right: 8px;
    padding: 11px;
    border-radius: ${({ theme }) => theme?.size?.border?.radius?.main};
    background-color: ${({ theme }) => theme.color?.general.darker};
    cursor: pointer;
    border: 1px solid ${({ theme }) => theme.color?.general.dark};
    display: none;

    @media only screen and (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
      right: 24px;
      bottom: 24px;
    }

    &.mobileView {
      right: 8px;
      bottom: calc(64px + env(safe-area-inset-bottom));
    }
  }

  .orangeText {
    color: ${({ theme }) => theme.color?.secondary.dark};
    display: contents;
  }

  .clamp-1-line {
    display: -webkit-box !important;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .clamp-2-lines {
    display: -webkit-box !important;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* calendar */
  div[data-slot='calendar'] {
    margin-top: 20px;
    padding: 10px;
    border-radius: 10px;
    background-color: #253a5e;
    border: 1px solid #2d4672;
    min-width: 210px;
    margin-left: 25px;
    color: #94A3B8;
  }

  div[data-slot='calendar'] *:focus-visible {
    outline: none;
  }

  div[data-slot='calendar'] div[role='spinbutton'] {
    caret-color: #000 !important;
  }

  div[data-slot='calendar'] > div[data-slot='base'] span[data-slot='label'],
  div[data-slot='calendar']
  > div[data-slot='base']
  div[aria-label='time zone, '] {
    display: none;
  }

  div[data-slot='calendar'] > div[data-slot='base'] div[data-slot='input'] {
    justify-content: center;
  }

  div[data-slot='calendar'] thead tr {
    padding: 0;
  }

  div[data-slot='calendar'] thead th {
    width: 14%;
  }

  div[data-slot='calendar'] tbody td {
    width: 14%;
  }

  div[data-slot='calendar'] tbody td span {
    border-radius: 10px;
    padding: 1px 0px 0;
  }

  div[data-slot='calendar'] tbody td span[data-outside-month='true'] {
    color: #475f8b;
  }

  div[data-slot='calendar'] tbody td span[data-selected='true'] {
    background-color: ${({ theme }) => theme.color?.general.darkest};
  }

  div[data-slot='calendar'] header span[data-slot='title'] {
    text-wrap: nowrap;
  }

  // ReactTexty styles
  [data-texty] {
    display: block !important;
  }

  [data-texty-tooltip] {
    background-color: ${({ theme }) => theme.color?.general.dark};
    max-width: 60vw;
    padding: 7px 10px;

    span:not(.tag) {
      color: ${({ theme }) => theme.color?.general.white};
    }

    [data-texty-arrow] {
      display: none;
    }
  }

  .scroll::-webkit-scrollbar {
    width: 2px;
    height: 2px;
  }

  .scroll::-webkit-scrollbar-track {
    border-radius: 14px;
    border-right: 1px solid #2d4672;
  }

  .scroll::-webkit-scrollbar-thumb {
    background-color: ${({ theme }) => theme.color?.general.light};
  }


  @keyframes banners-bg-move {
    0% {
      transform: translateZ(0) rotate(0.0001deg);
    }

    25% {
      transform: translate3d(-20px, 0, 0) rotate(0.0001deg);
    }

    50% {
      transform: translateZ(0) rotate(0.0001deg);
    }

    75% {
      transform: translate3d(20px, 0, 0) rotate(0.0001deg);
    }

    100% {
      transform: translateZ(0) rotate(0.0001deg);
    }
  }

  @keyframes banners-main-move {
    0% {
      transform: translateZ(0) rotate(0.0001deg);
    }

    25% {
      transform: translate3d(20px, 0, 0) rotate(0.0001deg);
    }

    50% {
      transform: translateZ(0) rotate(0.0001deg);
    }

    75% {
      transform: translate3d(-20px, 0, 0) rotate(0.0001deg);
    }

    100% {
      transform: translateZ(0) rotate(0.0001deg);
    }
  }

  @keyframes mascot-animation {
    0% {
      transform: translate3d(-100%, 0, 0);
    }
    100% {
      transform: translate3d(100%, 0, 0);
    }
  }

  @keyframes scale-down-center {
    0% {
      transform: scale(1);
    }
    100% {
      transform: scale(0);
    }
  }

  @keyframes scale-up-center {
    0% {
      transform: scale(0);
    }
    100% {
      transform: scale(1);
    }
  }

  @keyframes heartbeat {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(0.9);
    }
    100% {
      transform: scale(1);
    }
  }

  @keyframes marquee {
    0% {
      transform: translateX(100%);
    }
    100% {
      transform: translateX(0);
    }
  }

  .marquee-text {
    display: inline-block;
    padding-right: 70%;
    animation: marquee 20s linear infinite;
  }

  .mascot__gradient-stop {
    stop-color: white;
  }

  // hide recaptcha badge, but must keep terms of service text
  .grecaptcha-badge {
    visibility: hidden;
  }
`;

export default GlobalStyles;
