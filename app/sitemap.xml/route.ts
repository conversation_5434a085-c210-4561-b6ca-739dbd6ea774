import { NextRequest, NextResponse } from 'next/server'
import { LANGUAGES } from '@/app/i18n/settings'
import { getGames } from '@/app/api/getGames'
import { getProviders } from '@/app/api/getProviders'
import { getPosts } from '@/app/api/getPosts'
import { getPromotions } from '@/app/api/getPromotions'
import { getTournaments } from '@/app/api/getTournaments'

const BASE_URL = 'https://likecasino.com'

// Game page types
const GAME_PAGE_TYPES = ['casino', 'live-casino']

// Static game categories from navMenuEntities
const GAME_CATEGORIES = [
  'all', 'top', 'new', 'rtp', 'tournament', 'bonus', 'slots', 'jackpot',
  'megaways', 'buy-bonus', 'table', 'roulette', 'blackjack', 'baccarat',
  'poker', 'shows', 'other', 'crash', 'tables'
]

interface SitemapUrl {
  url: string
  lastModified: Date
  changeFrequency: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'
  priority: number
}

function generateSitemapXML(urls: SitemapUrl[]): string {
  const urlEntries = urls.map(({ url, lastModified, changeFrequency, priority }) => `
  <url>
    <loc>${url}</loc>
    <lastmod>${lastModified.toISOString()}</lastmod>
    <changefreq>${changeFrequency}</changefreq>
    <priority>${priority.toFixed(1)}</priority>
  </url>`).join('')

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urlEntries}
</urlset>`
}

export async function GET(request: NextRequest) {
  try {
    const sitemap: SitemapUrl[] = []

    // Add homepage for each language
    LANGUAGES.forEach(lang => {
      sitemap.push({
        url: `${BASE_URL}/${lang}`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 1.0,
      })
    })

    // Add game page types (casino, live-casino) for each language
    LANGUAGES.forEach(lang => {
      GAME_PAGE_TYPES.forEach(gamePageType => {
        sitemap.push({
          url: `${BASE_URL}/${lang}/${gamePageType}`,
          lastModified: new Date(),
          changeFrequency: 'daily',
          priority: 0.9,
        })
      })
    })

    // Add game categories for each language and game page type
    LANGUAGES.forEach(lang => {
      GAME_PAGE_TYPES.forEach(gamePageType => {
        GAME_CATEGORIES.forEach(category => {
          sitemap.push({
            url: `${BASE_URL}/${lang}/${gamePageType}/games/${category}`,
            lastModified: new Date(),
            changeFrequency: 'daily',
            priority: 0.8,
          })
        })
      })
    })

    // Add provider-filtered game category links
    const providerFilteredRoutes = [
      // Casino routes with provider_id=35
      { gamePageType: 'casino', category: 'crash', providerId: 35 },
      { gamePageType: 'casino', category: 'new', providerId: 35 },
      { gamePageType: 'casino', category: 'tables', providerId: 35 },
      // Live casino routes with provider_id=39
      { gamePageType: 'live-casino', category: 'blackjack', providerId: 39 },
    ]

    LANGUAGES.forEach(lang => {
      providerFilteredRoutes.forEach(({ gamePageType, category, providerId }) => {
        sitemap.push({
          url: `${BASE_URL}/${lang}/${gamePageType}/games/${category}?filters=provider_id=${providerId}`,
          lastModified: new Date(),
          changeFrequency: 'daily',
          priority: 0.8,
        })
      })
    })

    // Add static pages for each language and game page type
    const staticPages = [
      'help-center',
      'levels',
      'promotions',
      'providers',
      'tournaments',
      'top-wins',
      'favourites',
      'recent'
    ]

    LANGUAGES.forEach(lang => {
      GAME_PAGE_TYPES.forEach(gamePageType => {
        staticPages.forEach(page => {
          sitemap.push({
            url: `${BASE_URL}/${lang}/${gamePageType}/${page}`,
            lastModified: new Date(),
            changeFrequency: 'weekly',
            priority: 0.7,
          })
        })
      })
    })

    // Add profile pages for each language and game page type
    const profilePages = [
      'profile',
      'profile/personal-data',
      'profile/history',
      'profile/my-bonuses'
    ]

    LANGUAGES.forEach(lang => {
      GAME_PAGE_TYPES.forEach(gamePageType => {
        profilePages.forEach(page => {
          sitemap.push({
            url: `${BASE_URL}/${lang}/${gamePageType}/${page}`,
            lastModified: new Date(),
            changeFrequency: 'monthly',
            priority: 0.5,
          })
        })
      })
    })

    // Add info pages
    LANGUAGES.forEach(lang => {
      GAME_PAGE_TYPES.forEach(gamePageType => {
        const infoPages = [
          'terms-conditions',
          'privacy-policy',
          'responsible-gaming',
          'loyalty-program',
          'bonus-terms',
          'payment-methods',
          'kyc-policy',
        ]
        infoPages.forEach(page => {
          sitemap.push({
            url: `${BASE_URL}/${lang}/${gamePageType}/info/${page}`,
            lastModified: new Date(),
            changeFrequency: 'monthly',
            priority: 0.4,
          })
        })
      })
    })

    try {
      // Add dynamic content with error handling
      const [gamesResult, providersResult, promotionsResult, tournamentsResult, postsResult] = await Promise.allSettled([
        getGames(0, 500),
        getProviders(0, 100),
        getPromotions(0, 100),
        getTournaments(0, 100),
        getPosts(0, 200)
      ])

      // Add individual game pages
      if (gamesResult.status === 'fulfilled' && providersResult.status === 'fulfilled') {
        const { items: games } = gamesResult.value
        const { items: providers } = providersResult.value

        // Create a map of provider external_id to slug for URL generation
        const providerMap = new Map()
        providers.forEach(provider => {
          providerMap.set(provider.external_id, provider.slug)
        })

        // Add individual game pages (limit to reduce sitemap size)
        const limitedGames = games.slice(0, 300) // Limit to 300 most important games
        limitedGames.forEach(game => {
          const providerSlug = providerMap.get(game.provider_external_id) || 'unknown'
          
          LANGUAGES.forEach(lang => {
            GAME_PAGE_TYPES.forEach(gamePageType => {
              // Only add live games to live-casino and non-live games to casino
              if ((gamePageType === 'live-casino' && game.is_live) || 
                  (gamePageType === 'casino' && !game.is_live)) {
                sitemap.push({
                  url: `${BASE_URL}/${lang}/${gamePageType}/${providerSlug}/game/${game.slug}`,
                  lastModified: new Date(game.updated_at || game.created_at),
                  changeFrequency: 'monthly',
                  priority: 0.6,
                })
              }
            })
          })
        })
      }

      // Add promotion pages
      if (promotionsResult.status === 'fulfilled') {
        const { items: promotions } = promotionsResult.value
        promotions.forEach(promotion => {
          LANGUAGES.forEach(lang => {
            GAME_PAGE_TYPES.forEach(gamePageType => {
              sitemap.push({
                url: `${BASE_URL}/${lang}/${gamePageType}/promotions/${promotion.slug}`,
                lastModified: new Date(),
                changeFrequency: 'weekly',
                priority: 0.7,
              })
            })
          })
        })
      }

      // Add tournament pages
      if (tournamentsResult.status === 'fulfilled') {
        const { items: tournaments } = tournamentsResult.value
        tournaments.forEach(tournament => {
          LANGUAGES.forEach(lang => {
            GAME_PAGE_TYPES.forEach(gamePageType => {
              sitemap.push({
                url: `${BASE_URL}/${lang}/${gamePageType}/tournaments/${tournament.slug}`,
                lastModified: new Date(tournament.end_date),
                changeFrequency: 'daily',
                priority: 0.8,
              })
            })
          })
        })
      }

      // Add help center posts
      if (postsResult.status === 'fulfilled') {
        const { items: posts } = postsResult.value
        posts.forEach(post => {
          LANGUAGES.forEach(lang => {
            GAME_PAGE_TYPES.forEach(gamePageType => {
              const postType = post.post_type === 'faq' ? 'faq' : 'help-center/post'
              sitemap.push({
                url: `${BASE_URL}/${lang}/${gamePageType}/${postType}/${post.slug}`,
                lastModified: new Date(post.updated_at),
                changeFrequency: 'monthly',
                priority: 0.5,
              })
            })
          })
        })
      }

    } catch (dynamicError) {
      console.error('Error generating dynamic sitemap entries:', dynamicError)
      // Continue with static entries even if dynamic ones fail
    }

    // Sort sitemap by priority (highest first) and then by URL
    sitemap.sort((a, b) => {
      if (a.priority !== b.priority) {
        return b.priority - a.priority
      }
      return a.url.localeCompare(b.url)
    })

    const sitemapXML = generateSitemapXML(sitemap)

    return new NextResponse(sitemapXML, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600', // Cache for 1 hour
      },
    })

  } catch (error) {
    console.error('Error generating sitemap:', error)
    
    // Return a minimal sitemap in case of error
    const fallbackSitemap = LANGUAGES.map(lang => ({
      url: `${BASE_URL}/${lang}`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1.0,
    }))

    const fallbackXML = generateSitemapXML(fallbackSitemap)
    
    return new NextResponse(fallbackXML, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=300, s-maxage=300', // Cache for 5 minutes on error
      },
    })
  }
}
