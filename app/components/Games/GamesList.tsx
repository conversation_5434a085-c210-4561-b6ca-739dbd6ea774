'use client';
import React, { useEffect } from 'react';

import { IGame, getGames, getGamesTournaments } from '@/app/api/getGames.ts';
import { getGamesUserRecommended } from '@/app/api/server-actions/getRecommendedGames.ts';
import { Container, TilesGrid, Typography } from '@/atomic-design-components';
import { useLoadMoreObserver } from '@/hooks/useLoadMoreObserver.tsx';
import GameCard from './GameCard.tsx';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { useParams, useSearchParams } from 'next/navigation';
import { usePrevious } from '@/hooks/useReact';
import { convertFiltersToObject } from '@/utils/convertFiltersToObject.ts';
import { useTranslation } from '@/app/i18n/client';
import GamesListRecommended from '@/app/components/Games/GamesListRecommended.tsx';
import { withTheme } from 'styled-components';
import PanelWithIconAndText from '@/app/components/PanelWithIconAndText';
import LoadingGameCardsGrid from '@/app/components/Skeletons/LoadingGameCardsGrid.tsx';
import { MAX_CONTAINER_WIDTH } from '@/constants.ts';

export const GAMES_LIST_BREAKPOINTS = {
  '(min-width: 1430px)': {
    perView: 8,
    spacing: 16,
  },
  '(max-width: 1430px)': {
    perView: 7,
    spacing: 16,
  },
  '(max-width: 1300px)': {
    perView: 6,
    spacing: 16,
  },
  '(max-width: 1150px)': {
    perView: 5,
    spacing: 16,
  },
  '(min-width: 961px) and (max-width: 1050px)': {
    perView: 4,
    spacing: 16,
  },
  '(max-width: 961px)': {
    perView: 5,
    spacing: 16,
  },
  '(max-width: 800px)': {
    perView: 4,
    spacing: 16,
  },
  '(max-width: 650px)': {
    perView: 4,
    spacing: 16,
  },
  '(max-width: 500px)': {
    perView: 3,
    spacing: 8,
  },
  '(max-width: 321px)': {
    perView: 2,
    spacing: 8,
  },
};

const GamesList = ({
  breakpoints,
  limit,
  initialData,
  totalItems,
  filtersMain,
  page,
  isBadgeShown,
  category,
  maxLimit,
  passSetTableDataToCard,
  sort,
  itemsInRow = 8,
  withLoadMore = true,
  isInitiallyLoaded,
  isTournamentsPage,
  isRecommendedGames,
  tournamentId,
  cardComponent,
  cardProps,
  isGamesInsideModal,
  theme,
}: {
  breakpoints?: any;
  limit: number;
  initialData: IGame[];
  totalItems: number;
  filtersMain?: { [_: string]: string[] };
  page?: string;
  isBadgeShown?: boolean;
  category?: string;
  maxLimit?: number | null;
  sort?: string;
  itemsInRow?: number;
  passSetTableDataToCard?: boolean;
  withLoadMore?: boolean;
  isInitiallyLoaded?: boolean;
  isTournamentsPage?: boolean;
  isRecommendedGames?: boolean;
  tournamentId?: number | null;
  cardComponent?: any;
  cardProps?: { [key: string]: any };
  transformGamesItems?: any;
  isGamesInsideModal?: boolean;
  theme: any;
}) => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);
  const loadMoreRef = React.useRef<HTMLDivElement>(null);
  const { gamePageType = 'casino' } = useParams();
  const searchParams = useSearchParams();
  const searchString = searchParams.toString();
  const prevSearchString = usePrevious(searchString);
  const { user, isBonusFilterActive, prevIsBonusFilterActive } = useUser();
  const hasActiveWager = !!user?.active_wager;

  const Component = cardComponent || GameCard;
  const [tableData, setTableData] = React.useState(initialData);
  const [skip, setSkip] = React.useState(searchString ? 0 : initialData.length);
  const [inProgress, setInProgress] = React.useState(!isInitiallyLoaded);
  const [total, setTotal] = React.useState(totalItems);

  const maxItemsCount = maxLimit || total;

  const bonusFilter =
    isBonusFilterActive && gamePageType === 'casino'
      ? { wager_percentage__gte: [1] }
      : {};

  const searchFilters = searchString
    ? convertFiltersToObject(searchParams.get('filters') || '')
    : null;

  const filters = searchParams
    ? {
        ...filtersMain,
        is_live: [(gamePageType === 'live-casino').toString()],
        ...searchFilters,
        ...bonusFilter,
      }
    : {
        ...filtersMain,
        is_live: [(gamePageType === 'live-casino').toString()],
        ...bonusFilter,
      };

  const fetchData = async (skipParam: number) => {
    const { items, total } = isTournamentsPage
      ? await getGamesTournaments(skipParam, limit, {
          ...filters,
          tournament_id: [tournamentId],
        })
      : isRecommendedGames
        ? await getGamesUserRecommended(
            skipParam,
            limit,
            hasActiveWager
              ? {
                  wager_percentage__gte: [1],
                }
              : undefined,
          )
        : await getGames(skipParam, limit, filters, sort);

    return { items, total };
  };

  useEffect(() => {
    if (isRecommendedGames || category === 'favourites') {
      setTableData(initialData);
      setSkip(limit);
    }
  }, [isRecommendedGames, initialData, limit, category]);

  useEffect(() => {
    if (
      isRecommendedGames ||
      isTournamentsPage ||
      category?.includes('rtp') ||
      (category && ['favourites', 'recent'].includes(category))
    ) {
      return;
    }

    if (
      searchString !== (prevSearchString || '') ||
      (gamePageType === 'casino' &&
        prevIsBonusFilterActive !== null &&
        isBonusFilterActive !== prevIsBonusFilterActive)
    ) {
      setInProgress(true);
      fetchData(0)
        .then((res) => {
          setTableData(res?.items || []);
          setSkip(limit);
          setTotal(res?.total || 0);
        })
        .finally(() => {
          setInProgress(false);
        });
    }
  }, [
    searchString,
    prevSearchString,
    isBonusFilterActive,
    prevIsBonusFilterActive,
  ]);

  const loadNextPage = async () => {
    if (!withLoadMore || inProgress || maxItemsCount <= tableData?.length) {
      return;
    }
    setInProgress(true);
    const result = await fetchData(skip);

    const lastItemsToAddCountIsMaxLimit =
      maxLimit && maxLimit - tableData.length;
    const newItems = maxLimit
      ? result?.items.slice(0, lastItemsToAddCountIsMaxLimit)
      : result?.items;
    setTableData((prev: IGame[]) => {
      return [...prev, ...(newItems || [])];
    });
    setSkip((prev) => prev + limit);
    setInProgress(false);
    setTotal(result?.total || 0);
  };

  useLoadMoreObserver({
    loadNextPage,
    loadMoreRef,
    withLoadMore,
  });

  if (
    inProgress &&
    (isBonusFilterActive !== prevIsBonusFilterActive ||
      (!isInitiallyLoaded && searchParams.get('filters')))
  ) {
    return <LoadingGameCardsGrid />;
  }

  if (!isRecommendedGames && !isTournamentsPage && !tableData.length) {
    if (searchString || isBonusFilterActive) {
      return (
        <>
          <Typography
            text={t('noMatchedGames')}
            type='body2'
            justifyContent='center'
            color={theme.color?.general.lighter}
            textAlign='center'
            className='!mx-auto !my-9 md:max-w-[50%]'
          />
          <GamesListRecommended lng={lng} />
        </>
      );
    } else {
      return (
        <PanelWithIconAndText icon='disappointedFace' title={t('noGamesYet')} />
      );
    }
  }

  return (
    <>
      <Container
        flexDirection='column'
        fullWidth
        centered
        maxWidth={MAX_CONTAINER_WIDTH}
        dataLength={tableData.length}
        noPadding
        className='relative'
      >
        <TilesGrid
          itemsInRow={itemsInRow}
          breakpoints={breakpoints || GAMES_LIST_BREAKPOINTS}
          tiles={tableData.map((game: IGame, index: number) => (
            <Component
              item={game}
              key={index}
              index={index}
              page={page}
              setTableData={passSetTableDataToCard && setTableData}
              isBadgeShown={isBadgeShown}
              category={category}
              isGamesInsideModal={isGamesInsideModal}
              {...cardProps}
            />
          ))}
        />
        <div ref={loadMoreRef} className='absolute bottom-[30vh]' />
      </Container>
    </>
  );
};

export default withTheme(GamesList);
