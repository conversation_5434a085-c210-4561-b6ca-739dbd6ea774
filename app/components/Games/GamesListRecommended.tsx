'use client';
import { IGame } from '@/app/api/getGames.ts';
import { getGamesUserRecommended } from '@/app/api/server-actions/getRecommendedGames.ts';
import { useTranslation } from '@/app/i18n/client';
import { Typography } from '@/atomic-design-components';
import { LanguagesType } from '@/types/global.js';
import { useEffect, useState } from 'react';
import GamesList from './GamesList.tsx';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { useGamesInitialSelector } from '@/app/store/selectors.ts';
import { usePrevious } from '@/hooks/useReact';

const LIMIT = 24;

const GamesListRecommended = ({
  gamesToFilterOut,
  lng,
  className,
}: {
  gamesToFilterOut?: IGame[];
  lng: LanguagesType;
  className?: string;
}) => {
  const { t } = useTranslation(lng);
  const { user } = useUser();
  const hasActiveWager = !!user?.active_wager;
  const prevHasActiveWager = usePrevious(hasActiveWager);

  const {
    isInitiallyLoaded,
    saveGames,
    // total: totalInitial,
    items: itemsInitial,
  } = useGamesInitialSelector('recommended');

  const [games, setGames] = useState<IGame[]>(itemsInitial || []);
  const [totalItems, setTotalItems] = useState(itemsInitial?.length || 0);
  const [inProgress, setInProgress] = useState(false);

  const fetchGames = async () => {
    const { items, total } = await getGamesUserRecommended(
      0,
      LIMIT,
      hasActiveWager
        ? {
            wager_percentage__gte: [1],
          }
        : undefined,
    );

    if (saveGames) {
      saveGames({
        category: 'recommended',
        gamesResult: { items, total },
      });
    }

    const itemsFiltered = items?.filter(
      (game: IGame) =>
        !gamesToFilterOut?.some((g) => g.external_id === game.external_id),
    );

    setGames(itemsFiltered);
    setTotalItems(itemsFiltered?.length || 0);
  };

  useEffect(() => {
    if (
      !isInitiallyLoaded ||
      (prevHasActiveWager !== null && prevHasActiveWager !== hasActiveWager)
    ) {
      setInProgress(true);
      fetchGames().finally(() => {
        setInProgress(false);
      });
    }
  }, [isInitiallyLoaded, prevHasActiveWager, hasActiveWager]);

  useEffect(() => {
    const itemsFiltered = itemsInitial?.filter(
      (game: IGame) =>
        !gamesToFilterOut?.some((g) => g.external_id === game.external_id),
    );
    setGames(itemsFiltered);
    setTotalItems(itemsFiltered?.length || 0);
  }, [gamesToFilterOut]);

  if (!totalItems) {
    return null;
  }

  return (
    <div className={className}>
      {!inProgress && (
        <>
          <Typography
            text={`${t('recommended')} ${t('games')}`}
            margin='30px 0 20px'
            type='h2'
          />
          <GamesList
            initialData={games}
            totalItems={totalItems}
            limit={LIMIT}
            withLoadMore={false}
            isRecommendedGames={true}
            category='recommended'
            isInitiallyLoaded
          />
        </>
      )}
    </div>
  );
};

export default GamesListRecommended;
