'use client';
import { Typography } from '@/atomic-design-components';
import { LanguagesType } from '@/types/global';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation';
import parse from '@/utils/parseRichText.tsx';

const TournamentDescriptionCard = ({
  description,
  lng,
}: {
  description: any;
  lng: LanguagesType;
}) => {
  const text = getAvailableTranslation(description, lng);
  if (!text) return null;
  return (
    <div className='tournamentDescription relative rounded-lg bg-[#1E293B] px-2 py-4 md:p-6'>
      <Typography
        type='body2'
        className='description !block'
        text={parse(text)}
        displayCssProp='block'
      />
    </div>
  );
};

export default TournamentDescriptionCard;
