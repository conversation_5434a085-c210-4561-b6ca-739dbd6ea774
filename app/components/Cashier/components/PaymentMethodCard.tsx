'use client';
import { IPaymentCard } from '@/app/api/payments/getPaymentData.ts';
import { Typography } from '@/atomic-design-components';
import {
  LOGO_PLACEHOLDER_HORIZONTAL,
  PAYMENT_METHODS_GROUPS,
} from '@/constants';
import useWindowSize from '@/hooks/useWindowSize';
import { theme } from '@/theme';
import { getCurrencySymbol } from '@/utils/getCurrencySymbol.ts';
import clsx from 'clsx';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import { useState } from 'react';
import { StyledLine } from '../../NavMenu/styled';
import { StyledCard } from '../styled';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation.ts';

type PaymentMethodGroupKey = keyof typeof PAYMENT_METHODS_GROUPS;

export const INFINITY_SYMBOL = (
  <span className='ml-[3px] mt-[1px] text-[16px]'>&#8734;</span>
);

const PaymentMethodCard = ({
  item,
  index,
  className,
  setActiveCard,
  setActiveScreen,
  // isWithdrawal,
  isDisabled,
  userCurrency,
}: {
  item: IPaymentCard;
  index: number;
  className?: string;
  setActiveCard?: any;
  setActiveScreen: any;
  isDisabled?: boolean;
  isWithdrawal?: boolean;
  userCurrency: string;
}) => {
  const { lng } = useParams();
  const currencySymbol = getCurrencySymbol(userCurrency);

  const { width } = useWindowSize();
  const { isTouchDevice } = useIsTouchMobileView();
  const isTouchDeviceMd =
    isTouchDevice || (!!width && width < theme.breakpoints?.md);

  const [isLoaded, setIsLoaded] = useState(false);

  const onCardClick = () => {
    if (isDisabled) {
      return;
    }
    setActiveCard(item);
    setActiveScreen(
      'methods' in item ? 'paymentMethodsInGroup' : 'paymentAction',
    );
  };

  const getImageContent = () => {
    if ('methods' in item) {
      return (
        <Image
          alt={item.title || `Method ${index}`}
          placeholder={undefined}
          onLoad={() => setIsLoaded(true)}
          src={`/payments/${item.group as PaymentMethodGroupKey}.png`}
          width={isTouchDeviceMd ? 136 : 208}
          height={isTouchDeviceMd ? 35 : 50}
          style={{ objectFit: 'contain', width: '100%', height: 'auto' }}
          unoptimized
        />
      );
    } else if (item.photo_url) {
      return (
        <>
          {item.photo_url && !isLoaded && (
            <Image
              alt={
                item.title_platform ||
                item.title ||
                `Method ${index} placeholder`
              }
              placeholder={undefined}
              src={LOGO_PLACEHOLDER_HORIZONTAL}
              width={isTouchDeviceMd ? 136 : 208}
              height={isTouchDeviceMd ? 35 : 50}
              style={{ objectFit: 'contain', width: '100%', height: 'auto' }}
              className='placeholder'
              unoptimized
            />
          )}
          {item.photo_url && (
            <Image
              alt={item.title_platform || item.title || `Method ${index}`}
              placeholder={undefined}
              onLoad={() => setIsLoaded(true)}
              src={item.photo_url}
              width={isTouchDeviceMd ? 136 : 208}
              height={isTouchDeviceMd ? 35 : 50}
              style={{ objectFit: 'contain', width: '100%', height: 'auto' }}
              unoptimized
            />
          )}
        </>
      );
    } else {
      return (
        <Typography
          text={
            item.title_platform ||
            item.title ||
            ((item.group as keyof typeof PAYMENT_METHODS_GROUPS) &&
              getAvailableTranslation(
                PAYMENT_METHODS_GROUPS[item.group as PaymentMethodGroupKey],
                lng,
              ))
          }
        />
      );
    }
  };

  return (
    <StyledCard
      flexDirection='column'
      onClick={onCardClick}
      className={clsx(isDisabled && 'disabled')}
    >
      <div
        className={clsx(
          'methodCard',
          !item.photo_url && 'placeholder',
          !isLoaded && 'loading',
          className,
          'methods' in item && 'cardMethod',
        )}
      >
        {getImageContent()}
      </div>
      <StyledLine
        color={theme.color?.general.dark}
        className='w-full'
        margin='0'
      />
      <Typography
        className='py-1'
        type='label2'
        color={theme.color?.general.lighter}
      >
        {currencySymbol}
        {item.amount_min} - {item.amount_max ? currencySymbol : ' '}
        {item.amount_max || INFINITY_SYMBOL}
      </Typography>
    </StyledCard>
  );
};

export default PaymentMethodCard;
