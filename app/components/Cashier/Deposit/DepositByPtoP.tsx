'use client';
import { useEffect, useState } from 'react';
import clsx from 'clsx';
import { IMethod } from '@/app/api/payments/getPaymentData.ts';
import { IBonus } from '@/app/api/server-actions/bonuses.ts';
import { getAmountInitial } from '@/app/components/Cashier/Deposit/DepositByCrypto.tsx';
import { useTranslation } from '@/app/i18n/client';
import { Button, Loader, Typography } from '@/atomic-design-components';
import { LanguagesType } from '@/types/global';
import { CashierTabPageType } from '../CashierModal';
import DepositInputsBlock from './DepositInputsBlock';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider';
import { getPaymentUrlForP2p } from '@/app/api/payments/getPaymentUrlForP2p.ts';
import { useAlert } from '@/app/wrappers/AlertProvider.tsx';
import DepositHostToHost from '@/app/components/Cashier/Deposit/DepositHostToHost.tsx';
import { getPaymentInfoForP2p } from '@/app/api/payments/getPaymentInfoForP2p.ts';
import { getIp } from '@/app/api/getIP.ts';
import { PAYMENT_SETTINGS_BANKS } from '@/constants.ts';

const DepositByPtoP = ({
  lng,
  activeCard,
  amountError,
  setAmountError,
  latestPaidAmount,
  userCurrency,
  bonuses,
  activeTabPage,
  setActiveTabPage,
  setActiveScreen,
  selectedBonus,
  setSelectedBonus,
  savedBonus,
  setSavedBonus,
  inProgressSavingBonus,
  setInProgressSavingBonus,
}: {
  lng: LanguagesType;
  activeCard: IMethod;
  amountError: string;
  setAmountError: Function;
  latestPaidAmount?: number | undefined;
  userCurrency: string;
  bonuses?: IBonus[] | null;
  activeTabPage?: CashierTabPageType;
  setActiveTabPage: Function;
  setActiveScreen: Function;
  selectedBonus: number;
  setSelectedBonus: Function;
  savedBonus: number | null;
  setSavedBonus: Function;
  inProgressSavingBonus: boolean;
  setInProgressSavingBonus: Function;
}) => {
  const { t } = useTranslation(lng);
  const { showError } = useAlert();
  const { isTouchDevice } = useIsTouchMobileView();
  const [amount, setAmount] = useState(
    getAmountInitial(activeCard.amount_min, latestPaidAmount) || 0,
  );
  const [isPaymentBlockShown, setIsPaymentBlockShown] =
    useState<boolean>(false);
  const [p2pPaymentInfo, setP2pPaymentInfo] = useState<any>(null);

  const hasBonuses = !!bonuses && bonuses.length > 0;

  const [inProgress, setInProgress] = useState(false);

  const bankNames = activeCard?.h2h_bank_name;
  // const isBankSelectShown = bankNames?.length > 1;
  const bankOptions = PAYMENT_SETTINGS_BANKS.filter((bank) =>
    bankNames?.includes(bank.id),
  );
  const [bank] = useState<any>(null);

  useEffect(() => {
    if (setActiveTabPage) {
      setActiveTabPage('depositByPtoP');
    }
  }, []);

  const onPaymentButtonClick = async () => {
    setInProgress(true);
    const client_ip = await getIp();

    if (
      (activeCard.is_deposit_h2h || activeCard.cascadeId) &&
      setActiveTabPage
    ) {
      const requestData = {
        amount: amount,
        currency: userCurrency,
        payment_setting_ids: activeCard.cascadeId
          ? [...(activeCard.payment_settings_ids || [])]
          : [activeCard.id],
        callback_url: window.location.origin + '/' + lng,
        client_ip,
        payment_cascade_id: activeCard.cascadeId,
        bank_name: bankOptions?.length === 1 ? bankOptions[0].id : bank?.id,
      };

      const result = await getPaymentInfoForP2p(requestData);

      if (!result || result?.error) {
        setInProgress(false);
        showError(t('noAvailablePaymentDetails'));
        setP2pPaymentInfo(null);
      } else {
        setP2pPaymentInfo(result);
        setInProgress(false);
        setIsPaymentBlockShown(true);
        setActiveTabPage('payment');
      }
    } else {
      const result = await getPaymentUrlForP2p({
        amount: amount,
        currency: userCurrency,
        payment_setting_id: activeCard.id,
        callback_url: window.location.origin + '/' + lng,
        client_ip,
        bank_name: bankOptions?.length === 1 ? bankOptions[0].id : bank?.id,
      });
      if (result?.url) {
        window.open(result.url, '_self');
        setInProgress(false);
      }
      if (!result || result?.error) {
        setInProgress(false);
        showError(t('noAvailablePaymentDetails'));
      }
    }
  };

  return isPaymentBlockShown &&
    p2pPaymentInfo &&
    activeTabPage &&
    ['payment', 'paymentResult'].includes(activeTabPage) ? (
    <DepositHostToHost
      activeTabPage={activeTabPage}
      amount={amount}
      isTouchDevice={isTouchDevice}
      lng={lng}
      t={t}
      userCurrency={userCurrency}
      p2pPaymentInfo={p2pPaymentInfo}
      setActiveTabPage={setActiveTabPage}
      setActiveScreen={setActiveScreen}
    />
  ) : (
    <div
      className={clsx(
        'depositContentWrapper flex flex-col',
        isTouchDevice ? 'mobileView' : '',
      )}
    >
      <Typography
        text={t('deposit')}
        type='h1'
        className={clsx(isTouchDevice ? '!hidden' : 'ml-6 max-md:!hidden')}
      />
      <DepositInputsBlock
        lng={lng}
        activeCard={activeCard}
        amount={amount}
        setAmount={setAmount}
        amountError={amountError}
        setAmountError={setAmountError}
        userCurrency={userCurrency}
        bonuses={bonuses}
        hasBonuses={hasBonuses}
        className='md:mt-6'
        selectedBonus={selectedBonus}
        setSelectedBonus={setSelectedBonus}
        savedBonus={savedBonus}
        setSavedBonus={setSavedBonus}
        inProgressSavingBonus={inProgressSavingBonus}
        setInProgressSavingBonus={setInProgressSavingBonus}
      />
      <div
        id='paymentButton'
        className={clsx(
          !isTouchDevice &&
            'max-md:fixed max-md:bottom-2 max-md:w-[calc(100%-16px)]',
          isTouchDevice &&
            'fixed bottom-2 w-[calc(100%-16px)] md:w-[calc(100%-48px)]',
        )}
      >
        <Button
          text={t('toPayment')}
          fullWidth
          onClick={() => onPaymentButtonClick()}
          disabled={
            inProgress ||
            !amount ||
            (amountError && amountError !== 'enterMinAmountForBonus')
          }
        />
      </div>
      <Loader
        active={inProgress}
        size='80px'
        variant='circular'
        withOverlay={true}
      />
    </div>
  );
};

export default DepositByPtoP;
