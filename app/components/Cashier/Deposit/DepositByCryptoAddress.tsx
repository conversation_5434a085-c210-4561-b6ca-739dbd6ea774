import { getCurrencyRate } from '@/app/api/payments/getCurrencyRate.ts';
import { IMethod } from '@/app/api/payments/getPaymentData.ts';
import { CRYPTO_TOKENS } from '@/app/components/Cashier/CashierModal.tsx';
import { getAmountWithFeesApplied } from '@/app/components/Cashier/components/PaymentActionScreen.tsx';
import { StyledPaymentWindow } from '@/app/components/Cashier/styled.ts';
import { Button, Icon, Typography } from '@/atomic-design-components';
import { theme } from '@/theme.ts';
import { LanguagesType } from '@/types/global';
import { useCopyToClipboard } from '@/utils/useCopyToClipboard.ts';
import clsx from 'clsx';
import i18next from 'i18next';
import { QRCodeSVG } from 'qrcode.react';
import { useEffect, useState } from 'react';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider';

const DepositByCryptoAddress = ({
  activeCard,
  address,
  amount,
  className,
  // hasBonuses,
  lng,
  t,
  userCurrency,
  setActiveTabPage,
}: {
  activeCard: IMethod;
  address: string;
  amount: number;
  className?: string;
  // hasBonuses: boolean;
  lng: LanguagesType;
  t: typeof i18next.t;
  userCurrency: string;
  setActiveTabPage?: Function;
}) => {
  const [rate, setRate] = useState<number | null>(null);
  const { isTouchDevice } = useIsTouchMobileView();
  const copy = useCopyToClipboard(lng);

  const cryptoObject =
    CRYPTO_TOKENS[activeCard?.currency_platform as keyof typeof CRYPTO_TOKENS];
  const network = cryptoObject?.network || '';
  const multiplier = cryptoObject?.multiplierForMathCeil;
  const token = cryptoObject?.token || '';

  const amountToDeposit =
    rate && +amount
      ? Math.ceil(
          getAmountWithFeesApplied(
            +amount,
            activeCard.fee_percent_user,
            activeCard.fee_fixed_user,
            activeCard.exchange_fee_percent_deposit,
          ) *
            rate *
            multiplier,
        ) / multiplier
      : 0;

  useEffect(() => {
    if (setActiveTabPage) {
      setActiveTabPage('depositByCryptoAddress');
    }
  }, []);

  useEffect(() => {
    if (rate === null && userCurrency) {
      getCurrencyRate({
        from: userCurrency,
        to: token,
      })
        .then((rate) => {
          if (!rate?.error) {
            setRate(rate.value || 0);
          }
          if (rate?.error) {
            console.log(rate.error);
          }
        })
        .catch((error) => {
          console.log(error);
        });
    }
  }, [rate]);

  return (
    <StyledPaymentWindow
      flexDirection='column'
      alignItems='start'
      gap='8px'
      className={clsx(
        'horizontal', // hasBonuses ? 'horizontal' : ''
        'cryptoAddress overflow-auto',
        className,
        isTouchDevice ? 'mobileView' : '',
      )}
    >
      <Typography iconName='cardPlus' text={t('sendToAddress')} type='h3' />

      <div className='infoCard flex w-full flex-col items-start gap-2'>
        <div className='flex w-full flex-col items-center gap-4 rounded-lg bg-[#1E293B] px-2 py-4'>
          <QRCodeSVG
            value={address || ''}
            size={100}
            className='shrink-0 rounded-[6px] bg-[white] p-[5px]'
          />
          <div className='flex w-full flex-col gap-2'>
            <Typography
              type='body2'
              className='relative min-h-[40px] cursor-pointer justify-between rounded-lg bg-general-dark py-2 pl-3 pr-[38px] [overflow-wrap:anywhere]'
              text={address || ''}
              iconName='copy'
              iconProps={{
                className: 'order-1 !m-0 !ml-2 absolute right-3',
                width: 20,
                height: 20,
              }}
              onClick={() => {
                copy(address || '');
              }}
            />
            <Typography
              type='body2'
              className='cursor-pointer justify-between rounded-lg bg-general-dark px-3 py-2'
              iconName='copy'
              iconProps={{
                className: 'order-1 !m-0 !ml-2',
                width: 20,
                height: 20,
              }}
              onClick={() => {
                copy(amountToDeposit.toString());
              }}
            >
              {amountToDeposit} {token || ''}
            </Typography>
            <Typography type='label2'>{t('rateMayChange')}</Typography>
          </div>
          <div className='flex w-full flex-col gap-2'>
            <Button
              text={t('copyAddress')}
              fullWidth
              onClick={() => {
                copy(address || '');
              }}
              // Disable hover and active states on touch devices
              className={clsx(
                isTouchDevice ? '!bg-[#0083FF]' : 'max-md:!bg-[#0083FF]',
              )}
            />
          </div>
        </div>

        <div className='flex w-full items-start gap-2 rounded-lg bg-[#1E293B] px-2 py-2'>
          <Icon name='infoIcon' fill={theme.color?.general.lightest} />
          <div className='flex flex-col gap-1'>
            <Typography
              text={`${t('onlyUSDTshouldSent').replace(
                'USDT',
                token,
              )} ${network}`}
              type='sub1'
            />
            <Typography text={t('onlyUSDTshouldSentText')} />
          </div>
        </div>
      </div>
    </StyledPaymentWindow>
  );
};

export default DepositByCryptoAddress;
