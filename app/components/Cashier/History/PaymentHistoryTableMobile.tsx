import clsx from 'clsx';
import { IPaymentTransaction } from '@/app/api/payments/getPaymentTransactions.ts';
import PaymentCard from '@/app/components/Cashier/History/PaymentCard.tsx';
import { LanguagesType } from '@/types/global';
import React from 'react';
import { useLoadMoreObserver } from '@/hooks/useLoadMoreObserver.tsx';

const PaymentHistoryTableMobile = ({
  isTouchDevice,
  transactions,
  lng,
  loadMore,
  setTransactions,
  totalItems,
  limit,
}: {
  isTouchDevice: boolean;
  transactions: IPaymentTransaction[];
  lng: LanguagesType;
  loadMore: Function;
  setTransactions: Function;
  totalItems: number;
  limit: number;
}) => {
  const loadMoreRef = React.useRef<HTMLDivElement>(null);

  const [skip, setSkip] = React.useState(limit);

  const loadNextPage = async () => {
    if (totalItems <= transactions?.length) return;
    // setInProgress(true);
    const items = await loadMore(skip);

    setTransactions((prev: IPaymentTransaction[]) => [...prev, ...items]);
    setSkip((prev: number) => prev + limit);
    // setInProgress(false);
  };

  useLoadMoreObserver({ loadNextPage, loadMoreRef, withLoadMore: true });

  return (
    <div
      className={clsx(
        'relative order-1 flex flex-col gap-2',
        isTouchDevice ? '' : 'md:hidden',
      )}
    >
      {transactions.map((transaction: IPaymentTransaction) => (
        <PaymentCard
          key={transaction.transaction_id}
          item={transaction}
          lng={lng}
          isCashier
        />
      ))}
      <div ref={loadMoreRef} className='absolute bottom-[25vh]' />
    </div>
  );
};

export default PaymentHistoryTableMobile;
