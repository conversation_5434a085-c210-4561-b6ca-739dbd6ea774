'use client';
import { IMethod } from '@/app/api/payments/getPaymentData.ts';
import { getPaymentPreferences } from '@/app/api/payments/getPaymentPreferences.ts';
import { savePaymentPreferences } from '@/app/api/payments/savePaymentPreferences.ts';
import { triggerWithdrawalProcess } from '@/app/api/payments/triggerWithdrawalProcess.ts';
import AverageProcessingTime from '@/app/components/Cashier/components/AverageProcessingTime.tsx';
import Commissions from '@/app/components/Cashier/components/Commissions.tsx';
import AvailableWithdrawalAmount from '@/app/components/Cashier/Withdrawal/AvailableWithdrawalAmount.tsx';
import { useTranslation } from '@/app/i18n/client';
import { useAlert } from '@/app/wrappers/AlertProvider.tsx';
import { UserType, useUser } from '@/app/wrappers/userProvider.tsx';
import {
  Button,
  Checkbox,
  Input,
  Loader,
  Select,
  Typography,
} from '@/atomic-design-components';
import { theme } from '@/theme';
import { LanguagesType } from '@/types/global';
import { getCurrencySymbol } from '@/utils/getCurrencySymbol.ts';
import { useEffect, useRef, useState } from 'react';
import { StyledDepositInputsBlock } from '../styled';
import clsx from 'clsx';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation';
import getWithdrawalIntervals from '@/app/components/Cashier/components/getWithdrawalIntervals.ts';
import { getIp } from '@/app/api/getIP.ts';
import { isObjectEmpty } from '@/utils/object.ts';
import { PAYMENT_SETTINGS_BANKS } from '@/constants.ts';

export const countAvailableAmountWithBlocked = (
  balance: number,
  blockedAmount: number,
) => {
  // toFixed(0) to remove decimal points (that gives some weird calculations if not removed)
  return (
    (+(balance * 100).toFixed(0) - +(blockedAmount * 100).toFixed(0)) / 100
  );
};

const WithdrawalInputBlock = ({
  activeCard,
  activePageType,
  amountError,
  setAmountError,
  lng,
  userCurrency,
  className,
}: {
  activeCard: IMethod;
  amountError: string;
  setAmountError: Function;
  lng: LanguagesType;
  userCurrency: string;
  activePageType?: string;
  className?: string;
}) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const { isTouchDevice } = useIsTouchMobileView();
  const { t } = useTranslation(lng);
  const { user, setUser } = useUser();
  const availableAmountToWithdraw = user!.active_bonus_id
    ? countAvailableAmountWithBlocked(
        user!.account_balance,
        user!.blocked_amount,
      )
    : user!.account_balance;

  const min = activeCard!.amount_min;
  const max = activeCard!.amount_max;

  const [amount, setAmount] = useState(min);
  const [wallet, setWallet] = useState('');
  const [phone, setPhone] = useState('');
  const [card, setCard] = useState<number | null>(null);
  const [fullName, setFullName] = useState('');

  const bankNames = activeCard?.h2h_bank_name;
  const isBankSelectShown = bankNames?.length > 1;
  const bankOptions = PAYMENT_SETTINGS_BANKS.filter((bank) =>
    bankNames?.includes(bank.id),
  );
  const [bank, setBank] = useState<any>(null);

  const [inProgress, setInProgress] = useState(false);
  const [isSubmitPressed, setIsSubmitPressed] = useState(false);
  const [clientInfoError, setClientInfoError] = useState('');

  const [isRememberInfoChecked, setIsRememberInfoChecked] = useState(false);
  const savedPreferencesId = useRef(0);

  const { showAlert, showError } = useAlert();

  const currencySymbol = getCurrencySymbol(
    userCurrency as keyof typeof getCurrencySymbol,
  );

  // TODO: transfer this to the parent component (PaymentActionScreen)
  useEffect(() => {
    getPaymentPreferences(activeCard.id).then((preferences) => {
      if (!preferences?.data || isObjectEmpty(preferences.data)) {
        return;
      }
      savedPreferencesId.current = preferences.id;

      if (activeCard.payment_type === 'crypto') {
        setWallet(preferences.data.wallet_address || '');
        setIsRememberInfoChecked(!!preferences.data.wallet_address);
      }

      if (activeCard.payment_type !== 'crypto') {
        if (activeCard.is_phone_number) {
          setPhone(preferences.data.phone_number || '');
        } else {
          setCard(preferences.data.card_number || '');
        }
        if (isBankSelectShown && preferences.data.bank_name) {
          setBank(
            bankOptions.find((bank) => bank.id === preferences.data.bank_name),
          );
        }
        setFullName(preferences.data.card_holder || '');
        setIsRememberInfoChecked(
          !!(preferences.data.phone_number || preferences.data.card_number),
        );
      }
    });
  }, []);

  useEffect(() => {
    const minAmountError = amount < min && 'enterMinAmount';
    const maxAmountError =
      max && amount > max && 'amountExceedsWithdrawalLimit';
    const amountExceedsBalance =
      amount > availableAmountToWithdraw && 'amountExceedsBalance';

    const error = minAmountError || maxAmountError || amountExceedsBalance;

    if (error) {
      setAmountError(error);
    } else {
      setAmountError('');
    }
  }, [amount, min, max, availableAmountToWithdraw]);

  // validate client info fields
  useEffect(() => {
    if (!isSubmitPressed) {
      return;
    }

    if (activeCard.payment_type === 'crypto') {
      if (!wallet) {
        setClientInfoError('fillInRequiredFields');
      } else {
        setClientInfoError('');
      }
    } else {
      if (
        (activeCard.is_phone_number ? !phone : !card) ||
        (isBankSelectShown && !bank) ||
        !fullName
      ) {
        setClientInfoError('fillInRequiredFields');
      } else {
        setClientInfoError('');
      }
    }
  }, [
    card,
    wallet,
    isBankSelectShown,
    fullName,
    activeCard,
    phone,
    isSubmitPressed,
    bank,
  ]);

  const handleWithdrawal = async () => {
    setIsSubmitPressed(true);
    if (
      amountError ||
      clientInfoError ||
      (activeCard.payment_type === 'crypto'
        ? !wallet
        : (!card && !phone) || !fullName || (isBankSelectShown && !bank))
    ) {
      return;
    }
    setInProgress(true);
    const client_ip = await getIp();

    const isCascade =
      activeCard.cascadeId && activeCard.payment_settings_ids?.length;
    const cascade_payment_settings_ids = isCascade
      ? activeCard.payment_settings_ids?.slice(1)
      : undefined;

    triggerWithdrawalProcess({
      amount,
      bank_name: bankOptions?.length === 1 ? bankOptions[0].id : bank?.id,
      currency: userCurrency,
      card_number: card,
      card_holder: fullName,
      wallet_address: wallet,
      payment_setting_id: isCascade
        ? activeCard.payment_settings_ids![0]
        : activeCard.id,
      phone_number: phone,
      client_ip,
      payment_cascade_id: isCascade ? activeCard.cascadeId : undefined,
      cascade_payment_settings_ids,
    })
      .then((res) => {
        if (res && !res.error) {
          const withdrawalInterval = getWithdrawalIntervals(activeCard, t);
          showAlert({
            content: t('withdrawalRequestSuccess', { withdrawalInterval }),
            id: Date.now().toString(),
            type: 'success',
            timeout: 5000,
          });
          setAmount(activeCard!.amount_min);
          setUser((prev: UserType | null) =>
            prev
              ? {
                  ...prev,
                  account_balance: prev.account_balance - res.amount_user,
                }
              : null,
          );
        } else {
          showError(
            res?.error?.includes('daily limit')
              ? t('dailyWithdrawalLimitExceeded')
              : res?.error?.includes('account wrong status')
                ? t('withdrawalAccountWrongStatus')
                : res?.error?.includes('withdrawal blocked without deposit')
                  ? t('youNeedDepositBeforeWithdrawals')
                  : res?.error?.includes('e-mail must be verified')
                    ? t('emailVerificationRequiredWithdrawal')
                    : res?.error?.includes('phone must be verified')
                      ? t('phoneVerificationRequiredWithdrawal')
                      : res?.error?.includes('passport must be verified')
                        ? t('kycRequiredWithdrawal')
                        : res?.error || t('error'),
          );
        }

        savePaymentPreferences({
          savedPreferencesId: savedPreferencesId.current,
          player_id: user!.id,
          payment_setting_id: activeCard.id,
          bank_name: bank?.id,
          card_number: card,
          card_holder: fullName,
          wallet_address: wallet,
          phone_number: phone,
          isRememberInfoChecked,
        }).then((preferencesRes) => {
          if (!preferencesRes || preferencesRes.error) {
            showError(t('savingPaymentDataError'));
          } else if (!isRememberInfoChecked && res && !res.error) {
            setCard(null);
            setFullName('');
            setWallet('');
            setPhone('');
            setBank(null);
          }
          if (preferencesRes?.id) {
            savedPreferencesId.current = preferencesRes.id;
          }
        });
      })
      .catch((err) => {
        console.error(err);
      })
      .finally(() => {
        setInProgress(false);
        setIsSubmitPressed(false);
      });
  };

  return (
    <>
      <StyledDepositInputsBlock
        className={clsx(
          'withdrawalInputBlock flex w-full flex-col gap-4',
          isTouchDevice ? 'mobileView' : 'md:h-[calc(100dvh-150px)]',
          className,
        )}
      >
        <AvailableWithdrawalAmount
          className='mx-2 justify-start md:mx-6'
          t={t}
        />
        <div className='flex w-full flex-col items-start gap-2'>
          <Typography
            iconName='coins'
            text={t('chooseWithdrawalAmount')}
            type='h3'
          />
          <div className='flex w-full flex-col items-start gap-2'>
            <div className='flex w-full gap-2'>
              <Input
                name='amount'
                key='amount'
                type='number'
                ref={inputRef}
                onChange={(value: any) => {
                  setAmount(value);
                }}
                onFocus={() => {
                  inputRef.current?.select();
                }}
                fullWidth
                value={amount}
                labelTop={t('amount')}
                labelBottom={
                  <Typography type='label2'>
                    Min - {currencySymbol}
                    {min}
                    {!!max && (
                      <span>
                        , Max - {currencySymbol}
                        {max}
                      </span>
                    )}
                  </Typography>
                }
                iconRightProps={{
                  name: amount ? 'cross' : '',
                  onClick: () => setAmount(0),
                }}
                withButtonInside={!!availableAmountToWithdraw}
                buttonText={availableAmountToWithdraw ? t('max') : ''}
                onButtonClick={() =>
                  setAmount(
                    availableAmountToWithdraw < max
                      ? availableAmountToWithdraw
                      : max,
                  )
                }
                buttonProps={{
                  backgroundColor: theme.color?.general.dark,
                  className: amount ? 'maxButton withCross' : 'maxButton',
                  disabled: inProgress,
                }}
                error={t(amountError, {
                  amount:
                    currencySymbol +
                    (amountError === 'enterMinAmount'
                      ? min
                      : amountError === 'amountExceedsWithdrawalLimit'
                        ? max
                        : amountError === 'amountExceedsBalance'
                          ? availableAmountToWithdraw
                          : ''),
                })}
                disabled={inProgress}
              />
            </div>
          </div>
        </div>
        <div className='flex h-full w-full flex-col items-start gap-2'>
          <Typography
            iconName='creditCard'
            text={t('withdrawalDetails')}
            type='h3'
          />
          <div
            className={
              isBankSelectShown
                ? 'flex w-full flex-wrap gap-2'
                : 'flex w-full gap-2'
            }
          >
            {activeCard.payment_type === 'crypto' && (
              <Input
                name='wallet'
                key='wallet'
                onChange={(e: any) => {
                  const { value } = e.target;
                  setWallet(value);
                }}
                fullWidth
                value={wallet}
                labelTop={t('walletAddress')}
                error={t(clientInfoError)}
                disabled={inProgress}
              />
            )}
            {activeCard.payment_type !== 'crypto' &&
              (activeCard.is_phone_number ? (
                <Input
                  name='phone'
                  key='phone'
                  onChange={(e: any) => {
                    const { value } = e.target;
                    setPhone(value);
                  }}
                  fullWidth
                  value={phone}
                  labelTop={t('phoneNumber')}
                  error={!phone && t(clientInfoError)}
                  disabled={inProgress}
                />
              ) : (
                <Input
                  name='card'
                  key='card'
                  type='number'
                  onChange={(value: number | null) => {
                    setCard(value);
                  }}
                  fullWidth
                  // width={isBankSelectShown ? '100%' : 'calc(50% - 4px)'}
                  value={card}
                  labelTop={t('cardNumber')}
                  error={!card && t(clientInfoError)}
                  disabled={inProgress}
                />
              ))}

            {activeCard.payment_type !== 'crypto' && isBankSelectShown && (
              <Select
                error={!bank && t(clientInfoError)}
                labelVariant='label2'
                label={t('bankName')}
                className='banksSelect w-[calc(50%-4px)]'
                isSearchable={false}
                name='bank'
                onChange={(bank: any) => setBank(bank)}
                options={bankOptions}
                value={bank}
              />
            )}
            {activeCard.payment_type !== 'crypto' && (
              <Input
                name='fullName'
                key='fullName'
                className='w-[calc(50%-4px)]'
                onChange={(e: any) => {
                  const { value } = e.target;
                  setFullName(value);
                }}
                fullWidth={!isBankSelectShown}
                value={fullName}
                labelTop={t('firstLastName')}
                error={!fullName && t(clientInfoError)}
                disabled={inProgress}
              />
            )}
          </div>
          <Checkbox
            checked={isRememberInfoChecked}
            handleChange={() => {
              setIsRememberInfoChecked((prev) => !prev);
            }}
            name='rememberInfo'
            // value,
            variant='secondary'
            disabled={inProgress}
            label={t(
              activeCard.payment_type === 'crypto'
                ? 'rememberWallet'
                : activeCard.is_phone_number
                  ? 'rememberPhone'
                  : 'rememberCard',
            )}
          />
          <div className='mt-[10px] flex flex-col gap-[4px]'>
            <Typography>
              {getAvailableTranslation(activeCard?.comment, lng)}
            </Typography>
            <Commissions
              t={t}
              activeCard={activeCard!}
              currencySymbol={currencySymbol}
            />
            <AverageProcessingTime t={t} activeCard={activeCard!} />
          </div>
        </div>
      </StyledDepositInputsBlock>
      <div
        id='paymentButton'
        className={clsx(
          'max-md:fixed max-md:bottom-2 max-md:w-[calc(100%-16px)]',
          isTouchDevice &&
            'fixed bottom-2 w-[calc(100%-24px)] md:w-[calc(100%-48px)]',
        )}
      >
        <Button
          text={t('payout')}
          size='medium'
          disabled={
            inProgress || !amount
            //   ||
            // !!amountError ||
            // (activeCard.payment_type === 'crypto' && !wallet) ||
          }
          onClick={() => handleWithdrawal()}
          fullWidth
        />
      </div>
      <Loader
        active={inProgress && activePageType === 'withdrawalPtoP'}
        size='80px'
        variant='circular'
        withOverlay={true}
      />
    </>
  );
};

export default WithdrawalInputBlock;
