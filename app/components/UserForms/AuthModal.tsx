'use client';
import clsx from 'clsx';
import { IBanner } from '@/app/api/getBanners';
import Image from 'next/image';
import { useEffect, useRef, useState } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import ModalDialog from '@/app/components/ModalDialog';
import { useTranslation } from '@/app/i18n/client';
import { Tabs, Typography } from '@/atomic-design-components';
import useClickOutside from '@/hooks/useClickOutside';
import { closeModal } from '@/utils/closeModal';
import { openModal } from '@/utils/openModal';
import LoginForm from '../LoginForm/LoginForm';
import SignUpForm from '../SignUpForm/SignUpForm';
import { useNavigation } from '@/app/wrappers/NavigationProvider.tsx';
import { useUser } from '@/app/wrappers/userProvider';
import { isAnyModalOpen } from '@/utils/modalUtils';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider';
import { LanguagesType } from '@/types/global';
import { useSystemData } from '@/app/wrappers/systemDataProvider.tsx';
import { useRegisterModalToOpenByUrl } from '@/app/wrappers/OpenModalByUrlProvider.tsx';

const AuthModal = () => {
  const pathname = usePathname();
  const lng: LanguagesType = pathname.split('/')[1];
  const { t } = useTranslation(lng);
  const { isTouchDevice } = useIsTouchMobileView();
  const ref = useRef<HTMLDialogElement>(null);
  const { user } = useUser();
  const searchParams = useSearchParams();
  useClickOutside(ref);

  const register = useRegisterModalToOpenByUrl();
  useEffect(() => {
    if (ref.current) {
      register('authModal');
    }
  }, [ref?.current]);

  const [{ banners: bannersAll }] = useSystemData();

  const { activeAuthTab, setActiveAuthTab } = useNavigation();
  const [inProgress, setInProgress] = useState(false);
  const [signupBanner, setSignupBanner] = useState<{
    desktop?: IBanner;
    mobile?: IBanner;
  }>({});
  const [loginBanner, setLoginBanner] = useState<{
    desktop?: IBanner;
    mobile?: IBanner;
  }>({});
  const [bannersFetched, setBannersFetched] = useState(false);

  const onTabClick = (e: Event, idx: number) => {
    setActiveAuthTab(idx === 0 ? 'signUp' : 'login');
  };
  const getTabTitle = (tab: any) => (
    <Typography text={t(tab.title)} justifyContent='center' type='sub2' />
  );

  const tabs = [
    { id: 'signup', title: 'signup' },
    { id: 'login', title: 'login' },
  ];
  const tabsContents = [
    <SignUpForm
      key={0}
      banner={signupBanner.mobile}
      isInProgress={setInProgress}
    />,
    <LoginForm
      key={1}
      banner={loginBanner.mobile}
      isInProgress={setInProgress}
    />,
  ];

  const fetchBanner = async () => {
    const bannersAuth =
      bannersAll?.filter(
        (banner) =>
          banner.banner_type === 'sign_up' || banner.banner_type === 'login',
      ) || [];
    setSignupBanner({
      desktop: bannersAuth?.find(
        (item) =>
          item.display_mode === 'desktop' && item.banner_type === 'sign_up',
      ),
      mobile: bannersAuth?.find(
        (item) =>
          item.display_mode === 'mobile' && item.banner_type === 'sign_up',
      ),
    });
    setLoginBanner({
      desktop: bannersAuth?.find(
        (item) =>
          item.display_mode === 'desktop' && item.banner_type === 'login',
      ),
      mobile: bannersAuth?.find(
        (item) =>
          item.display_mode === 'mobile' && item.banner_type === 'login',
      ),
    });
    setBannersFetched(true);
  };

  useEffect(() => {
    if (!bannersFetched) fetchBanner();
  }, []);

  useEffect(() => {
    if (searchParams.get('promo_code') && !user?.id) {
      setActiveAuthTab('signUp');
      openModal('authModal');
    }
    const timer = setTimeout(() => {
      const hasShownSignupModal = sessionStorage.getItem('hasShownSignupModal');
      if (!user?.id && !hasShownSignupModal && !isAnyModalOpen(document)) {
        setActiveAuthTab('signUp');
        openModal('authModal');
        sessionStorage.setItem('hasShownSignupModal', 'true');
      }
    }, 60000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <ModalDialog
      id='authModal'
      ref={ref}
      onClose={() => {
        if (activeAuthTab === 'login') {
          closeModal('authModal');
        } else {
          openModal('getBonusModal', true, false, true);
        }
      }}
      closeButtonPosition='right-2 top-2 md:right-4 md:top-4'
    >
      <div className='flex h-full w-full'>
        <div
          className={clsx(
            'relative hidden h-full w-1/2 transition-opacity duration-300 md:block',
            signupBanner.desktop || loginBanner.desktop
              ? 'opacity-100'
              : 'opacity-0',
          )}
        >
          <Typography
            className='absolute p-12'
            text={
              activeAuthTab === 'signUp'
                ? getAvailableTranslation(
                    signupBanner.desktop?.title || '',
                    lng,
                  )
                : getAvailableTranslation(loginBanner.desktop?.title || '', lng)
            }
            type='h1'
            fontSize='40px'
            lineHeight='58px'
          />
          {bannersFetched && (
            <Image
              alt='Auth Banner'
              src={
                activeAuthTab === 'signUp'
                  ? signupBanner.desktop?.photo_main_url ?? ''
                  : loginBanner.desktop?.photo_main_url ?? ''
              }
              height={2512}
              width={1824}
              className={clsx(
                'h-full w-full object-cover',
                isTouchDevice ? '' : 'rounded-s-[8px]',
              )}
            />
          )}
        </div>
        <div className='mx-auto flex h-full flex-col pb-3 pt-[56px] md:px-12 md:py-12'>
          <Tabs
            activeTabProp={activeAuthTab === 'login' ? 1 : 0}
            onTabChange={onTabClick}
            getTabTitle={getTabTitle}
            tabsTitles={tabs}
            tabsContents={tabsContents}
            hideNavBtns
            className={clsx('authTabs', activeAuthTab)}
            isDisabled={inProgress}
          />
        </div>
      </div>
    </ModalDialog>
  );
};

export default AuthModal;
