'use client';
import styled from 'styled-components';
import clsx from 'clsx';
import { useParams } from 'next/navigation';
import { Button, Icon, Typography } from '@/atomic-design-components';
import { theme } from '@/theme.ts';
import { StyledLine } from '@/app/components/Footer/styled.ts';
import { useTranslation } from '@/app/i18n/client';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import ProgressBar from '@/app/components/ProgressBar';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation.ts';
import { ITranslations } from '@/types/global';
import { changeZerosToText } from '@/utils/changeNumberZerosToText.ts';
import { useUserLevels } from '@/app/components/Profile/Dashboard/UserLevelsProvider.tsx';
import LevelBonus from '@/app/components/Levels/LevelBonus.tsx';
import { getCurrencySymbol } from '@/utils/getCurrencySymbol.ts';
import Link from 'next/link';
import useWindowSize from '@/hooks/useWindowSize.ts';
import { useCookies } from 'react-cookie';
import { DEFAULT_CURRENCY, DEFAULT_POINT_VALUE } from '@/constants';
import { transformLevelPointsToLikes } from '@/utils/transformLevelPointsToLikes.ts';

const StyledLevelBonus = styled(LevelBonus)`
  flex-basis: 100%;
  position: relative;
  padding-top: 3px;

  .bonusTexts {
    width: calc(100% - 60px);

    .typography {
      max-width: unset;
    }
  }

  .doubleWidthLine {
    top: 100%;
  }

  @media only screen and (min-width: ${({ theme }) =>
      theme.breakpoints?.sm}px) {
    .doubleWidthLine {
      width: 200%;
      top: calc(100% + 9px);
    }

    &.halfWidth {
      flex-basis: 50%;
      max-width: 50%;
    }
  }

  .body1 {
    font-size: 16px;
    line-height: 24px;
    margin-top: 3px;
  }
`;

export const StyledBonusProfile = styled.div`
  @media only screen and (min-width: ${({ theme }) =>
      theme.breakpoints?.sm}px) {
    padding-right: 14px;
    &.leftPadding {
      padding-left: 14px;
      padding-right: 0;
      border-left: 1px solid ${theme.color?.general.dark};
    }
  }
`;

const LevelCard = () => {
  const { lng, gamePageType } = useParams();
  const { t } = useTranslation(lng);
  const { user } = useUser();
  const [cookies] = useCookies(['userCurrency']);
  const { width } = useWindowSize();

  const { currentLevel, nextLevel, bonuses, inProgress } = useUserLevels();
  const currency = user?.currency || cookies?.userCurrency || DEFAULT_CURRENCY;
  const currencySymbol = getCurrencySymbol(currency);

  if (!user) {
    return;
  }

  const likeValue =
    currentLevel?.point_value || nextLevel?.point_value || DEFAULT_POINT_VALUE;

  const likesLeft = changeZerosToText(
    Math.round(
      (transformLevelPointsToLikes(user.next_level_points, likeValue) -
        transformLevelPointsToLikes(user.level_current_points, likeValue)) *
        100,
    ) / 100,
    t,
  );

  const getBonusesRendered = () => {
    const cashbackIntervalDaysCurrent =
      currentLevel?.cashback_interval_days || 0;
    const cashbackIntervalDaysNext = nextLevel?.cashback_interval_days || 0;
    const bonusesToRender = [];

    if (
      (currentLevel?.cashback_percentage || 0) <
      (nextLevel?.cashback_percentage || 0)
    ) {
      bonusesToRender.push({
        currValue: (currentLevel?.cashback_percentage || 0) + '%',
        nextValue: nextLevel?.cashback_percentage + '%',
        titleKey: 'cashback',
      });
    }

    if (cashbackIntervalDaysCurrent > cashbackIntervalDaysNext) {
      bonusesToRender.push({
        currValue:
          cashbackIntervalDaysCurrent +
          ' ' +
          t('day', { count: +cashbackIntervalDaysCurrent }),
        nextValue:
          cashbackIntervalDaysNext +
          ' ' +
          t('day', { count: +cashbackIntervalDaysNext }),
        titleKey: 'cashbackTimer',
      });
    }

    if (
      (currentLevel?.max_daily_withdrawal || 0) <
      (nextLevel?.max_daily_withdrawal || 0)
    ) {
      bonusesToRender.push({
        currValue:
          currencySymbol +
          changeZerosToText(currentLevel!.max_daily_withdrawal || 0, t),
        nextValue:
          currencySymbol +
          changeZerosToText(nextLevel!.max_daily_withdrawal, t),
        titleKey: 'withdrawalLimit',
      });
    }

    const currentBirthdayBonus = bonuses.find(
      (bonus) => bonus.external_id === currentLevel?.bonus_birthday_external_id,
    );
    const nextBirthdayBonus = bonuses.find(
      (bonus) => bonus.external_id === nextLevel?.bonus_birthday_external_id,
    );

    if (
      (currentBirthdayBonus?.bonus_amount || 0) <
      (nextBirthdayBonus?.bonus_amount || 0)
    ) {
      bonusesToRender.push({
        currValue:
          currencySymbol +
          changeZerosToText(currentBirthdayBonus?.bonus_amount || 0, t),
        nextValue:
          currencySymbol +
          changeZerosToText(nextBirthdayBonus?.bonus_amount || 0, t),
        titleKey: 'birthdayGift',
      });
    }

    // setInProgress(false);
    return bonusesToRender;
  };

  const bonusesToRender = getBonusesRendered();
  const isBonusesNumberOdd = bonusesToRender?.length % 2 !== 0;
  const bonusesCount = bonusesToRender.length;

  return (
    <div className='flex flex-col rounded-lg bg-[#1E293B] p-2 sm:p-4'>
      <div className='flex flex-col gap-2'>
        {user?.active_bonus_id && (
          <div className='flex gap-2 rounded-lg bg-general-dark p-2'>
            <Icon
              name='infoIcon'
              fill={theme.color?.secondary.dark}
              margin='-2px 0 0 0'
            />
            <Typography text={t('activeBonusBetsDoNotCount')} type='sub1' />
          </div>
        )}
        <Typography
          text={
            getAvailableTranslation(
              currentLevel?.title as ITranslations,
              lng,
            ) || `Level ${user?.level_number}`
          }
          type='h1'
        />
        <ProgressBar
          currentProgress={user?.level_current_points || 0}
          total={user?.next_level_points || 0}
          color={theme.color?.status.success}
        />
        <div className='mt-1 flex justify-between'>
          <Typography
            text={t(+likesLeft === 1 ? 'likeLeft' : 'likesLeft', {
              likes: likesLeft,
            })}
            type='label1'
            color={theme.color?.general.lightest}
          />
          <Typography
            text='→'
            type='label1'
            color={theme.color?.general.lightest}
          />
          <Typography
            text={
              getAvailableTranslation(nextLevel?.title as ITranslations, lng) ||
              nextLevel?.name
            }
            type='label1'
            color={theme.color?.general.lightest}
          />
        </div>
      </div>

      <StyledLine margin='16px 0' />

      <Typography text={t('atNextLevel')} type='label1' margin='0 0 2px 0' />

      {inProgress ? (
        <>
          <div className='mb-3 mt-3 flex items-center justify-between'>
            <div className='h-12 w-12 rounded-full bg-gray-700'></div>
            <div className='ml-2 flex-1'>
              <div className='mb-1 h-4 w-1/4 rounded bg-gray-700'></div>
              <div className='h-4 w-1/6 rounded bg-gray-700'></div>
            </div>
            <div className='h-8 w-1/6 rounded bg-gray-700'></div>
          </div>
          <StyledLine />
        </>
      ) : (
        <div className='flex flex-wrap'>
          <StyledLevelBonus
            isActiveLevel={false}
            currencySymbol={currencySymbol}
            bonus={bonuses.find(
              (bonus) =>
                bonus.external_id === nextLevel?.bonus_level_up_external_id,
            )}
            className={clsx(
              'profile mb-4 mt-3 h-[48px]',
              bonusesCount && isBonusesNumberOdd && 'halfWidth',
            )}
          >
            <StyledLine
              margin='12px 0'
              color={theme.color?.general.dark}
              className={clsx(
                'absolute top-full',
                bonusesCount && isBonusesNumberOdd ? 'doubleWidthLine' : '',
              )}
            />
          </StyledLevelBonus>

          {bonusesToRender?.map((bonus, i) => {
            const isIndexEven = i % 2 === 0;

            return (
              <StyledBonusProfile
                className={clsx(
                  'relative mb-4 mt-2 flex w-full grow sm:mt-3 sm:w-1/2 sm:flex-col',
                  ((isBonusesNumberOdd && isIndexEven) ||
                    (!isBonusesNumberOdd && !isIndexEven)) &&
                    'leftPadding',
                )}
                key={i}
              >
                <Typography
                  text={t(bonus.titleKey)}
                  type='sub2'
                  className='basis-1/2 !text-[14px] !font-medium !leading-5 sm:basis-1 sm:!text-[16px] sm:!font-semibold sm:!leading-6'
                />
                <div className='flex grow justify-between'>
                  <Typography
                    type='body2'
                    text={bonus.currValue}
                    className='rounded-lg py-1 !text-[14px] !leading-5 sm:!text-[16px] sm:!leading-6'
                  />
                  <Typography type='body2' text='→' margin='0 0 0 -10px' />
                  <Typography
                    type='body2'
                    text={bonus.nextValue}
                    className='rounded-lg bg-[#489F37] px-2 py-1 !text-[14px] !leading-5 sm:!text-[16px] sm:!leading-6'
                  />
                </div>
                {((!isBonusesNumberOdd && isIndexEven) ||
                  width! <= theme.breakpoints?.sm ||
                  (isBonusesNumberOdd && !isIndexEven)) && (
                  <StyledLine className='absolute bottom-[-12px] sm:bottom-[-14px] sm:!w-[200%]' />
                )}
              </StyledBonusProfile>
            );
          })}
        </div>
      )}
      <Link
        href={`/${lng}/${gamePageType}/levels`}
        className='mt-2 sm:mt-[14px]'
      >
        <Button text={t('allLevels')} variant='transparent' fullWidth />
      </Link>
    </div>
  );
};

export default LevelCard;
