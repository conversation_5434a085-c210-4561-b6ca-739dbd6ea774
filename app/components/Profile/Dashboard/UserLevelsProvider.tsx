'use client';
import {
  useContext,
  createContext,
  ReactNode,
  useState,
  useEffect,
} from 'react';
import { UserType, useUser } from '@/app/wrappers/userProvider.tsx';
import { getLevels, ILevel } from '@/app/api/getLevels.ts';
import { getBonuses, IBonus } from '@/app/api/getBonuses.ts';
import Loading from '@/app/[lng]/[gamePageType]/profile/(dashboard)/loading.tsx';

export const useUserLevels = () => useContext(UserLevelsContext);

export const UserLevelsProvider = ({
  children,
  userUpdated,
}: {
  children: ReactNode;
  userUpdated: UserType;
}) => {
  const userLevels = useUserLevelsContext(userUpdated);

  if (userLevels.inProgress) {
    return <Loading />;
  }

  return (
    <UserLevelsContext.Provider value={userLevels}>
      {children}
    </UserLevelsContext.Provider>
  );
};

const useUserLevelsContext = (userUpdated: UserType) => {
  const { user, setUser } = useUser();

  const [currentLevel, setCurrentLevel] = useState<ILevel | null>(null);
  const [nextLevel, setNextLevel] = useState<ILevel | null>(null);

  const [bonuses, setBonuses] = useState<IBonus[]>([]);
  const [inProgress, setInProgress] = useState(true);

  useEffect(() => {
    setUser(userUpdated);
  }, []);

  useEffect(() => {
    if (!user) {
      setInProgress(false);
      return;
    }
    getLevels(0, 2, {
      currency: [user!.currency],
      number: [user!.level_number, user!.level_number + 1],
      // name: [`level${user!.level_number}`, `level${user!.level_number + 1}`],
    })
      .then((result) => {
        console.log('user level result', result);
        const userLevels = result?.items;
        const currentLevel = userLevels?.find(
          (level) =>
            (level.number || +level.name.replace('level', '')) ===
            user!.level_number,
        );

        setCurrentLevel(currentLevel || null);
        const nextLevel = userLevels?.find(
          (level) =>
            (level.number || +level.name.replace('level', '')) ===
            user!.level_number + 1,
        );
        setNextLevel(nextLevel || null);

        if (currentLevel && nextLevel) {
          // const externalIds = []
          getBonuses(0, 3, {
            external_id: [
              currentLevel.bonus_birthday_external_id!,
              nextLevel.bonus_level_up_external_id!,
              nextLevel.bonus_birthday_external_id!,
            ],
          })
            .then((result) => {
              setBonuses(result?.items);
            })
            .catch((error) => {
              console.error('level bonuses error', error);
            })
            .finally(() => {
              setInProgress(false);
            });
        } else {
          setInProgress(false);
        }
      })
      .catch((error) => {
        console.error('user levels error', error);
      });
  }, [user?.level_number]);

  return { currentLevel, nextLevel, bonuses, inProgress };
};

const UserLevelsContext = createContext<
  ReturnType<typeof useUserLevelsContext>
>({
  currentLevel: null,
  nextLevel: null,
  bonuses: [],
  inProgress: true,
});
