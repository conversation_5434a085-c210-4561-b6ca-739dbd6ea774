import styled from 'styled-components';
import clsx from 'clsx';
import Link from 'next/link';
import { openModal } from '@/utils/openModal.ts';
import { ALL_PATHS_WITH_AUTHORIZATION } from '@/app/config/navMenuEntities.ts';
import { Button } from '@/atomic-design-components';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { useNavigation } from '@/app/wrappers/NavigationProvider.tsx';
import { closeModal } from '@/utils/closeModal.ts';

const StyledLink = styled.a`
  #promotionModal & {
    text-decoration: none;
  }
`;

const TargetUrlButton = ({
  target_url,
  buttonText,
  isPromoButton,
  isTouchDevice,
}: {
  target_url: string;
  buttonText: string;
  isPromoButton?: boolean;
  isTouchDevice?: boolean;
}) => {
  const { user, setNextPathAfterLogin } = useUser();
  const isUserAuthorized = !!user?.id;
  const { setActiveAuthTab } = useNavigation();

  const isInnerTargetUrl = target_url?.includes(
    process.env.NEXT_PUBLIC_MAIN_DOMAIN || 'likecasino.com',
  );

  const isSignupModalShown =
    !isUserAuthorized &&
    target_url &&
    buttonText &&
    isInnerTargetUrl &&
    ALL_PATHS_WITH_AUTHORIZATION.some((path) => target_url.includes(path));

  const withDepositBtn =
    isInnerTargetUrl &&
    (target_url?.includes('cashier') || target_url?.includes('deposit'));

  const buttonUrl =
    target_url &&
    (isInnerTargetUrl
      ? target_url
          .replace('likecasino.com', '')
          // .replace(process.env.NEXT_PUBLIC_MAIN_DOMAIN || 'likecasino.com', '')
          .replace('https://', '')
      : target_url);

  // parse button
  const button = withDepositBtn ? (
    <Button
      variant='primary'
      text={buttonText}
      onClick={(e: any) => {
        e.preventDefault();
        if (isPromoButton) {
          closeModal('promotionModal');
        }

        if (isUserAuthorized) {
          openModal('cashierModal');
        } else {
          setActiveAuthTab('signUp');
          openModal('authModal');
        }
      }}
      className={
        isPromoButton
          ? clsx(
              !isTouchDevice && 'sm:ml-auto sm:w-max',
              'w-full min-w-[100px]',
            )
          : 'w-fit'
      }
    />
  ) : (
    <StyledLink
      as={isInnerTargetUrl ? Link : 'a'}
      href={isSignupModalShown ? '' : buttonUrl}
      className={isPromoButton ? 'w-full' : 'w-fit'}
      target={isInnerTargetUrl ? undefined : '_blank'}
      rel={isInnerTargetUrl ? undefined : 'noopener noreferrer'}
    >
      <Button
        variant='primary'
        text={buttonText}
        className={
          isPromoButton
            ? clsx(
                !isTouchDevice && 'sm:ml-auto sm:w-max',
                'w-full min-w-[100px]',
              )
            : undefined
        }
        onClick={
          isSignupModalShown
            ? () => {
                setNextPathAfterLogin(buttonUrl);
                if (isPromoButton) {
                  closeModal('promotionModal');
                }
                setActiveAuthTab('signUp');
                openModal('authModal');
              }
            : () => {
                if (isInnerTargetUrl && isPromoButton) {
                  closeModal('promotionModal');
                }
              }
        }
      />
    </StyledLink>
  );

  return button;
};

export default TargetUrlButton;
