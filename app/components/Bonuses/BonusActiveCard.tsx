'use client';
import dayjs from 'dayjs';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { Fragment, useEffect, useRef, useState } from 'react';
import ReactTexty from 'react-texty';
import clsx from 'clsx';
import { IBonus } from '@/app/api/server-actions/bonuses.ts';
import { ActiveCardBgImage } from '@/app/components/Bonuses/ActiveCardBgImage.tsx';
import { useTranslation } from '@/app/i18n/client';
import { useUnfinishedGame } from '@/app/wrappers/UnfinishedGameProvider.tsx';
import {
  ActiveFreespinsType,
  ActiveWagerType,
  UserType,
  useUser,
} from '@/app/wrappers/userProvider.tsx';
import { Button, FlexRow, Typography } from '@/atomic-design-components';
import { theme } from '@/theme.ts';
import { useTransformDateFromNowInSeconds } from '@/utils/dates';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation.ts';
import { getCurrencySymbol } from '@/utils/getCurrencySymbol.ts';
import { openModal } from '@/utils/openModal';
import { StyledBonusActiveCard } from './styled';
import { getCurrentUser } from '@/app/api/server-actions/getCurrentUser.ts';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider.tsx';

const BonusActiveCard = ({
  bonusTemplateData,
  isMobile,
  className,
}: {
  isMobile?: boolean;
  bonusTemplateData?: IBonus;
  className?: string;
}) => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);
  const { user, setUser } = useUser();
  const { isTouchDevice } = useIsTouchMobileView();

  const { unfinishedGameUrl } = useUnfinishedGame();
  const isGameUnfinished = user?.active_wager && unfinishedGameUrl;

  const activeBonusData: ActiveWagerType | ActiveFreespinsType | null =
    user!.active_wager || user!.active_freespins;

  const currentDateInUTC = useRef(dayjs().utc());

  const [isBonusActiveWithZeroTimer, setIsBonusActiveWithZeroTimer] =
    useState<boolean>(false);

  const lastDateTimeToPlay =
    !!activeBonusData?.time_to_play_seconds &&
    currentDateInUTC.current?.add(
      activeBonusData?.time_to_play_seconds,
      'second',
    );

  const date = useTransformDateFromNowInSeconds(
    lastDateTimeToPlay,
    lng,
    true,
  ) as Array<string>;

  const dateText = ['dayShort', 'hourShort', 'minShort', 'secShort'];
  let currDates = date || [];
  let currDateText = dateText;

  if (date[0] === '0') {
    currDates = date.slice(1);
    currDateText = dateText.slice(1);
  }

  useEffect(() => {
    if (!date || isBonusActiveWithZeroTimer) return;
    if (
      date.every((el: string) => el === '0' || el === '00') ||
      date.some((el: string) => el.includes('-'))
    ) {
      getCurrentUser().then((userRes: UserType) => {
        setUser(userRes);
        if (userRes.active_wager || userRes.active_freespins) {
          setIsBonusActiveWithZeroTimer(true);
        }
      });
    }
  }, [date, isBonusActiveWithZeroTimer]);

  if (!activeBonusData || !bonusTemplateData) {
    return (
      <div
        className={clsx(
          'mb-4 flex flex-col items-center gap-4 rounded-lg bg-[#1E293B] px-6 py-7',
          className,
        )}
      >
        {/* Icon */}
        <div className='h-12 w-12 animate-pulse rounded bg-gray-600'></div>

        {/* Title */}
        <div className='h-4 w-3/4 animate-pulse rounded bg-gray-600'></div>

        {/* Description */}
        <div className='h-4 w-5/6 animate-pulse rounded bg-gray-600'></div>
        <div className='h-4 w-2/3 animate-pulse rounded bg-gray-600'></div>
      </div>
    );
  }

  const isWager = user!.active_wager;
  const isFreespins = user!.active_freespins;
  const isDepositBonus = [
    'wager_deposit',
    'freespins_deposit',
    'wager_freespins_deposit',
  ].includes(user!.active_bonus_template_type);

  const currencySymbol = getCurrencySymbol(user!.currency);

  const betPerSpin =
    isFreespins &&
    (user?.active_bonus_template_type === 'freespins_deposit'
      ? (bonusTemplateData.min_deposit_amount *
          (bonusTemplateData.deposit_amount_percentage / 100)) /
        bonusTemplateData.freespins_amount
      : bonusTemplateData.bonus_amount / bonusTemplateData.freespins_amount);

  const goalSum =
    user!.active_wager?.goal_sum ||
    (user?.active_wager?.wager_amount
      ? user.active_wager.wager_amount * user!.active_wager?.multiplier
      : 0);

  const progress =
    ((user!.active_wager?.current_progress || 0) / goalSum) * 100;

  const showZeroTimer =
    currDates.some((el: string) => el.includes('-')) ||
    isBonusActiveWithZeroTimer;

  return (
    <StyledBonusActiveCard
      flexDirection='column'
      className={clsx(
        className,
        'p-2 max-md:mt-2 md:p-4',
        isWager && 'isWager',
      )}
    >
      <ActiveCardBgImage />
      <Typography
        className={clsx(
          'z-10 ml-[-17px] self-start rounded-br-[12px] rounded-tr-[12px] ',
          isTouchDevice ? '!hidden' : 'max-md:!hidden',
        )}
        text={t('active')}
        fontSize='12px'
        fontWeight={theme.font.weight.bold}
        textAlign='center'
        textTransform='uppercase'
        padding='4px 16px'
        lineHeight='14px'
        backgroundColor={theme.color?.status.success}
        color={theme.color?.general.lightest}
      />

      <Typography
        text={
          getAvailableTranslation(bonusTemplateData.title, lng) ||
          bonusTemplateData.name ||
          (isWager && t('wager'))
        }
        type='h3'
        justifyContent='start'
        className={clsx('z-[1] w-full', isTouchDevice ? '' : 'md:!hidden')}
      />

      {!!currDates?.length && (
        <div className='dateWrapper mx-4 mt-2 flex w-full justify-center gap-4 md:mt-4 md:gap-2'>
          {(showZeroTimer ? ['00', '00', '00'] : currDates).map(
            (el: string, idx: number) => (
              <Fragment key={idx}>
                {idx !== 0 && (
                  <Typography text=':' displayCssProp='block' type='h2' />
                )}
                <div className='flex basis-6 flex-col items-center md:basis-8'>
                  <Typography
                    text={el}
                    type='h2'
                    suppressHydrationWarning
                    color={theme.color?.general.lightest}
                  />
                  <Typography
                    text={t(
                      showZeroTimer
                        ? ['hour', 'min', 'sec']?.[idx]
                        : currDateText[idx],
                    )}
                    type='label2'
                    color={theme.color?.general.lighter}
                  />
                </div>
              </Fragment>
            ),
          )}
        </div>
      )}

      {isWager && (
        <div className='relative mt-4 flex h-[4px] w-full'>
          <div
            className='absolute left-0 top-0 z-[2] h-full rounded-full'
            style={{
              width: `${progress}%`,
              backgroundColor: theme.color?.status.success,
            }}
          />
          <div className='absolute left-0 top-0 z-[1] h-full w-full overflow-hidden rounded-full bg-general-darker' />
        </div>
      )}

      {isWager && (
        <div className={clsx('mt-3 flex w-full', isMobile && 'mb-4')}>
          <Typography
            type='label1'
            color={theme.color?.general.lighter}
            className='basis-1/2'
          >
            % {t('wagerProgress')}:
          </Typography>
          <Typography
            type='label1'
            color={theme.color?.general.lightest}
            className='basis-1/2'
            displayCssProp='block'
            textAlign='end'
          >
            {currencySymbol}
            {user!.active_wager?.current_progress || 0}/{goalSum || 0}
          </Typography>
        </div>
      )}

      <FlexRow flexDirection='column' className='content z-10 w-full'>
        {!isMobile && (
          <div className='mt-4 flex w-full border-b-[1px] border-b-general-lightest border-opacity-10 py-2'>
            <Typography
              type='body2'
              padding='0 8px 0 0'
              color={theme.color?.general.lighter}
              className='basis-1/2'
            >
              {t('bonusName')}:
            </Typography>
            <Typography
              as={ReactTexty}
              type='body2'
              text={
                getAvailableTranslation(bonusTemplateData.title, lng) ||
                bonusTemplateData.name ||
                (isWager && t('wager'))
              }
              color={theme.color?.general.lightest}
              fontWeight={theme.font.weight.bold}
              className='basis-1/2'
            />
          </div>
        )}

        {isFreespins && bonusTemplateData?.game_name && (
          <div className='flex w-full border-b-[1px] border-b-general-lightest border-opacity-10 py-2'>
            <Typography
              type='body2'
              padding='0 8px 0 0'
              color={theme.color?.general.lighter}
              className='basis-1/2'
            >
              {t('game')}:
            </Typography>
            <Typography
              as={ReactTexty}
              type='body2'
              color={theme.color?.general.lightest}
              fontWeight={theme.font.weight.bold}
              className='basis-1/2'
            >
              {bonusTemplateData?.game_name}
            </Typography>
          </div>
        )}

        {isFreespins && (
          <div className='flex w-full border-b-[1px] border-b-general-lightest border-opacity-10 py-2'>
            <Typography
              type='body2'
              padding='0 8px 0 0'
              color={theme.color?.general.lighter}
              className='basis-1/2'
            >
              {t('quantity')}:
            </Typography>
            <Typography
              type='body2'
              color={theme.color?.general.lightest}
              fontWeight={theme.font.weight.bold}
              className='basis-1/2'
            >
              {bonusTemplateData.freespins_amount}FS
            </Typography>
          </div>
        )}

        {isFreespins && (
          <div className='flex w-full border-b-[1px] border-b-general-lightest border-opacity-10 py-2'>
            <Typography
              type='body2'
              padding='0 8px 0 0'
              color={theme.color?.general.lighter}
              className='basis-1/2'
            >
              {t('bet')}:
            </Typography>
            <Typography
              type='body2'
              color={theme.color?.general.lightest}
              fontWeight={theme.font.weight.bold}
              className='basis-1/2'
            >
              {currencySymbol}
              {betPerSpin}
            </Typography>
          </div>
        )}

        {isWager &&
          isDepositBonus &&
          !!bonusTemplateData.deposit_amount_percentage && (
            <div className='flex w-full border-b-[1px] border-b-general-lightest border-opacity-10 py-2'>
              <Typography
                type='body2'
                padding='0 8px 0 0'
                color={theme.color?.general.lighter}
                className='basis-1/2'
              >
                {t('percentage')}:
              </Typography>
              <Typography
                type='body2'
                text={bonusTemplateData.deposit_amount_percentage + '%'}
                color={theme.color?.general.lightest}
                fontWeight={theme.font.weight.bold}
                className='basis-1/2'
              />
            </div>
          )}

        {isWager && 'wager_amount' in activeBonusData && (
          <div className='flex w-full border-b-[1px] border-b-general-lightest border-opacity-10 py-2'>
            <Typography
              type='body2'
              padding='0 8px 0 0'
              color={theme.color?.general.lighter}
              className='basis-1/2'
            >
              {t('bonusAmount')}:
            </Typography>
            <Typography
              type='body2'
              color={theme.color?.general.lightest}
              fontWeight={theme.font.weight.bold}
              className='basis-1/2'
            >
              {currencySymbol}
              {activeBonusData.wager_amount}
            </Typography>
          </div>
        )}

        {isWager &&
          'deposit_amount' in activeBonusData &&
          !!activeBonusData.deposit_amount && (
            <div className='flex w-full border-b-[1px] border-b-general-lightest border-opacity-10 py-2'>
              <Typography
                type='body2'
                padding='0 8px 0 0'
                color={theme.color?.general.lighter}
                className='basis-1/2'
              >
                {t('depositAmount')}:
              </Typography>
              <Typography
                type='body2'
                color={theme.color?.general.lightest}
                fontWeight={theme.font.weight.bold}
                className='basis-1/2'
              >
                {currencySymbol}
                {activeBonusData.deposit_amount}
              </Typography>
            </div>
          )}

        {!!(
          bonusTemplateData.wager_multiplier || bonusTemplateData.multiplier
        ) && (
          <div className='flex w-full border-b-[1px] border-b-general-lightest border-opacity-10 py-2'>
            <Typography
              type='body2'
              padding='0 8px 0 0'
              color={theme.color?.general.lighter}
              className='basis-1/2'
            >
              {t('wager')}:
            </Typography>
            <Typography
              type='body2'
              color={theme.color?.general.lightest}
              fontWeight={theme.font.weight.bold}
              className='basis-1/2'
            >
              x
              {bonusTemplateData.wager_multiplier ||
                bonusTemplateData.multiplier}
            </Typography>
          </div>
        )}
        {isFreespins && (
          <Link
            className='w-full'
            href={
              '/' +
              lng +
              '/casino/' +
              bonusTemplateData.game_provider_slug +
              '/game/' +
              bonusTemplateData.game_slug
            }
          >
            <Button
              variant='primary'
              text={t('play')}
              className='mb-1 mt-4'
              fullWidth
            />
          </Link>
        )}
      </FlexRow>
      <Button
        variant='transparent'
        className='cancelButton mt-2'
        text={t('cancelBonus')}
        fullWidth
        backgroundColor='transparent'
        onClick={(e: any) => {
          e.preventDefault();
          if (isGameUnfinished) {
            openModal('unfinishedGameModal');
          } else {
            openModal('cancelBonusModal');
          }
        }}
      />
    </StyledBonusActiveCard>
  );
};

export default BonusActiveCard;
