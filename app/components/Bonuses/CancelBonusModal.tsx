'use client';
import { useParams } from 'next/navigation';
import { useState } from 'react';
import { useTranslation } from '@/app/i18n/client';
import ModalDialog from '@/app/components/ModalDialog';
import { closeModal } from '@/utils/closeModal.ts';
import { UserType, useUser } from '@/app/wrappers/userProvider.tsx';
import { cancelBonus } from '@/app/api/server-actions/bonuses.ts';
import { useAlert } from '@/app/wrappers/AlertProvider.tsx';
import { useUserBonuses } from '@/app/wrappers/UserBonusesProvider.tsx';
import { getCurrencySymbol } from '@/utils/getCurrencySymbol.ts';

const CancelBonusModal = () => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);

  const { user, setUser } = useUser();
  const { getFilteredBonuses, setInProgressBonuses } = useUserBonuses();

  const { showError } = useAlert();

  const [inProgress, setInProgress] = useState(false);

  return (
    <ModalDialog
      id='cancelBonusModal'
      title={t('wantCancelBonus')}
      text={
        t('bonusWillLost') +
        (user?.blocked_amount
          ? ' ' +
            t('bonusBlockedAmount', {
              blockedAmount:
                getCurrencySymbol(user?.currency) + user?.blocked_amount,
            })
          : '')
      }
      withButtons
      inProgress={inProgress}
      onSecondButtonClick={() => {
        setInProgress(true);
        setInProgressBonuses(true);

        cancelBonus(user!.active_bonus_id, user!.active_bonus_type)
          .then((result) => {
            if (!result || result.error) {
              setInProgress(false);
              setInProgressBonuses(false);
              showError(result?.error || t('error'));
            } else {
              // setIsBonusFilterActive(false);
              setUser(result.user as UserType);
              // console.log('getFilteredBonuses right on bonus cancel');
              getFilteredBonuses();
            }
            closeModal('cancelBonusModal');
          })
          .catch((error) => {
            showError(error || t('error'));
          })
          .finally(() => {
            setInProgress(false);
            setInProgressBonuses(false);
          });
      }}
      contentPaddings='max-sm:px-4 max-sm:pb-3 max-sm:pt-6 sm:p-6'
      closeButtonPosition='right-4 top-4 sm:right-6 sm:top-6'
    />
  );
};

CancelBonusModal.displayName = 'CancelBonusModal';
export default CancelBonusModal;
