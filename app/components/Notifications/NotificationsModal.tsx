'use client';
import { INotification } from '@/app/api/server-actions/getNotifications.ts';
import { useTranslation } from '@/app/i18n/client';
import { useNewNotification } from '@/app/wrappers/NewNotificationProvider.tsx';
import { Tabs, Typography } from '@/atomic-design-components';
import { useParams } from 'next/navigation';
import { memo, useEffect, useRef, useState } from 'react';
import NotificationsList from './NotificationsList';
import { StyledNotificationsModal } from './styled';
import { NOTIFICATIONS_TO_SHOW } from '@/constants.ts';
import NoNotifications from '@/app/components/Notifications/components/NoNotifications.tsx';
import useClickOutside from '@/hooks/useClickOutside';
import { useRegisterModalToOpenByUrl } from '@/app/wrappers/OpenModalByUrlProvider.tsx';

const NOTIFICATIONS_TABS = ['offer', 'system'];

const NotificationsModal = memo(function NotificationsModal() {
  const { lng } = useParams();
  const { t } = useTranslation(lng);

  const ref = useRef<HTMLDialogElement>(null);
  useClickOutside(ref);

  const register = useRegisterModalToOpenByUrl();
  useEffect(() => {
    if (ref.current) {
      register('notificationsModal');
    }
  }, [ref?.current]);

  const [activeTab, setActiveTab] = useState(0);
  const [offerNotifications, setOfferNotifications] = useState<
    INotification[] | null
  >(null);
  const [systemNotifications, setSystemNotifications] = useState<
    INotification[] | null
  >(null);

  const {
    newNotification,
    inProgress,
    allNotifications,
    allNotificationsCount,
  } = useNewNotification();

  useEffect(() => {
    if (allNotifications && !offerNotifications && !systemNotifications) {
      if (allNotifications.length) {
        setTimeout(
          () =>
            allNotifications.forEach((item) => {
              if (
                NOTIFICATIONS_TO_SHOW.offer.includes(item.notification_type)
              ) {
                setOfferNotifications((prev) => [item, ...(prev || [])]);
              } else {
                setSystemNotifications((prev) => [item, ...(prev || [])]);
              }
            }),
          0,
        );
      } else {
        setOfferNotifications([]);
        setSystemNotifications([]);
      }
    }
  }, [allNotifications, offerNotifications, systemNotifications]);

  useEffect(() => {
    if (
      newNotification?.notification_type &&
      allNotifications !== null &&
      !allNotifications?.some(
        (notification) => notification.id === newNotification?.id,
      )
    ) {
      if (
        NOTIFICATIONS_TO_SHOW.offer.includes(newNotification.notification_type)
      ) {
        const newNotifications =
          newNotification.notification_type === 'new_bonuses'
            ? newNotification.newBonusesMessages || []
            : [newNotification];

        setOfferNotifications((prev) => [...newNotifications, ...(prev || [])]);
      } else {
        setSystemNotifications((prev) => [newNotification, ...(prev || [])]);
      }
    }
  }, [newNotification, allNotifications]);

  const onTabClick = (e: Event, idx: number) => {
    setActiveTab(idx);
  };

  const getTabTitle = (tab: any, idx: any) => {
    const notifications =
      tab === 'system' ? systemNotifications : offerNotifications;
    const newNotifications = notifications?.filter(
      (notification) => notification.status !== 'read',
    );
    return (
      <div className='flex items-center justify-center gap-2'>
        <Typography text={t(tab)} type='body2' />
        {!!newNotifications?.length && (
          <Typography
            text={newNotifications.length}
            className='h-4 w-4 rounded-full bg-[#0083FF]'
            justifyContent='center'
            alignItems='center'
            fontSize='10px'
            lineHeight='10px'
            backgroundColor={activeTab === idx ? '#0083FF' : '#1E293B'}
          />
        )}
      </div>
    );
  };

  const tabsContents = [
    <NotificationsList
      key={0}
      type='offer'
      // limit={10}
      initialData={offerNotifications}
      setData={setOfferNotifications}
      inProgress={inProgress}
    />,
    <NotificationsList
      key={1}
      type='system'
      // limit={10}
      initialData={systemNotifications}
      setData={setSystemNotifications}
      inProgress={inProgress}
    />,
  ];

  return (
    <StyledNotificationsModal id='notificationsModal' ref={ref}>
      <div className='flex h-full w-full flex-col'>
        <div className='flex h-full flex-col gap-4'>
          <Typography
            text={t('notifications')}
            type='h1'
            className='px-2 pt-3 md:px-6 md:pt-6'
          />
          {!inProgress && !allNotificationsCount ? (
            <NoNotifications t={t} />
          ) : (
            <Tabs
              activeTabProp={activeTab}
              onTabChange={onTabClick}
              getTabTitle={getTabTitle}
              tabsTitles={NOTIFICATIONS_TABS}
              tabsContents={tabsContents}
              hideNavBtns
              className='notificationsTabs overflow-hidden'
            />
          )}
        </div>
      </div>
    </StyledNotificationsModal>
  );
});

NotificationsModal.displayName = 'NotificationsModal';
export default NotificationsModal;
