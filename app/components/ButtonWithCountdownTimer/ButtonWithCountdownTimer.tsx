import { useState, useEffect, useRef } from 'react';
import { Button } from '@/atomic-design-components';

const ButtonWithCountdownTimer = ({
  secondsInitial = 60,
  buttonProps = { text: '' },
  t,
}: {
  secondsInitial: number;
  buttonProps?: any;
  t: Function;
}) => {
  const [secondsLeft, setSecondsLeft] = useState(secondsInitial);
  const intervalRef: any = useRef(null);

  useEffect(() => {
    setSecondsLeft(secondsInitial);
  }, [secondsInitial]);

  const startInterval = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    intervalRef.current = setInterval(() => {
      setSecondsLeft((prev) => {
        if (prev <= 1) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  useEffect(() => {
    startInterval();

    return () => {
      // cleanup on unmount
      clearInterval(intervalRef.current);
    };
  }, [secondsInitial]);

  // derive minutes and seconds
  const mins = Math.floor(secondsLeft / 60);
  const secs = secondsLeft % 60;

  // format as “m:ss” with leading zero for seconds
  const formatted = `${mins < 10 ? '0' : ''}${mins}:${secs < 10 ? '0' : ''}${secs}`;

  const text = buttonProps.text;

  const onButtonClick = (e: any) => {
    e.preventDefault();
    if (secondsLeft > 0) return;
    if (buttonProps.onClick) {
      buttonProps.onClick(e);
    }
    // setSecondsLeft(minutes * 60);
    // startInterval();
  };

  const timerWidth = 'w-[44px]'; //secondsLeft >= 600 ? 'w-[43px]' : 'w-[37px]';

  return (
    <>
      <Button
        {...buttonProps}
        disabled={secondsLeft > 0 || buttonProps.disabled}
        onClick={onButtonClick}
      >
        {secondsLeft <= 0 ? text : text + ' ' + t('in')}
        {secondsLeft > 0 && <span className={timerWidth}>{formatted}</span>}
      </Button>
    </>
  );
};

export default ButtonWithCountdownTimer;
